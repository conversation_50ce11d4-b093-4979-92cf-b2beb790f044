
#!/usr/bin/env python3
"""
显卡驱动安装
支持平台: Ubuntu/Debian
架构: 通用
"""

def install():
    """安装显卡驱动"""
    commands = [
        # 创建日志目录
        "mkdir -p logs",
        # 更新软件包列表
        "echo '更新软件包列表...' | tee -a logs/drivers_install.log",
        "sudo apt-get update",
        # 安装驱动管理工具
        "echo '安装驱动管理工具...' | tee -a logs/drivers_install.log",
        "sudo apt-get install -y ubuntu-drivers-common",
        # 检查可用驱动
        "echo '检查可用驱动...' | tee -a logs/drivers_install.log",
        "sudo ubuntu-drivers devices | tee -a logs/drivers_install.log",
        # 自动安装推荐驱动
        "echo '正在自动安装推荐驱动...' | tee -a logs/drivers_install.log",
        "sudo ubuntu-drivers autoinstall",
        "echo '驱动安装完成，请重启系统以应用更改。' | tee -a logs/drivers_install.log",
        "echo '✅ 显卡驱动安装完成！请重启系统以应用更改。'"
    ]
    return commands

def uninstall():
    """卸载显卡驱动"""
    commands = [
        # 创建日志目录
        "mkdir -p logs",
        # 开始卸载
        "echo '开始卸载显卡驱动...' | tee -a logs/drivers_install.log",
        # 卸载ubuntu-drivers-common
        "echo '卸载驱动管理工具...' | tee -a logs/drivers_install.log",
        "sudo apt-get remove -y ubuntu-drivers-common || true",
        "sudo apt-get autoremove -y",
        "echo '已卸载驱动管理工具' | tee -a logs/drivers_install.log",
        # 安全提示
        "echo '⚠️ 注意：已安装的显卡驱动未被卸载，以避免影响系统显示' | tee -a logs/drivers_install.log",
        "echo '如需卸载特定驱动，请手动执行相关命令' | tee -a logs/drivers_install.log",
        "echo '显卡驱动管理工具卸载成功' | tee -a logs/drivers_install.log",
        "echo '✅ 显卡驱动管理工具卸载完成！'"
    ]
    return commands

def check_status():
    import subprocess

    try:
        result = subprocess.run(["dpkg", "-l", "ubuntu-drivers-common"],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               timeout=5)
        if result.returncode == 0:
            print("🟢 状态: 已安装")
            return 0
        else:
            print("🔴 状态: 未安装")
            return 2
    except Exception:
        print("🔴 状态: 未安装")
        return 2

if __name__ == "__main__":
    import sys
    import subprocess

    def run_commands(commands):
        """执行命令列表"""
        for cmd in commands:
            print(f"执行: {cmd}")
            try:
                result = subprocess.run(cmd, shell=True, check=True,
                                      capture_output=True, text=True)
                if result.stdout:
                    print(result.stdout)
            except subprocess.CalledProcessError as e:
                print(f"❌ 错误: {e}")
                if e.stderr:
                    print(e.stderr)
                return False
        return True

    # 主逻辑
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall":
        print("🗑️ 开始卸载显卡驱动...")
        run_commands(uninstall())
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        # 安装前检查状态
        print("🔍 检查显卡驱动安装状态...")
        status_code = check_status()

        if status_code == 0:
            print("ℹ️ 显卡驱动管理工具已经安装，跳过安装步骤")
        else:
            print("🚀 开始安装显卡驱动...")
            if run_commands(install()):
                print("🔍 安装完成后再次检查状态...")
                check_status()
