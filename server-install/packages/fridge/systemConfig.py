#!/usr/bin/env python3
"""
系统配置优化
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys
import subprocess
import shutil
import pwd
import time

def get_user_info():
    sudo_user = os.environ.get('SUDO_USER')
    if sudo_user:
        try:
            user_info = pwd.getpwnam(sudo_user)
            return user_info.pw_name, user_info.pw_dir, user_info.pw_uid, user_info.pw_gid
        except KeyError:
            pass
    return "mxhou", "/home/<USER>", 1000, 1000

def run_command_sync(command, description="", capture_output=False, silent=False):
    if not silent:
        print("-" * 50)
        if description:
            print(f"🔄 {description}")
        print(f"执行: {command}")

    if capture_output:
        result = subprocess.run(command, shell=True, check=False, capture_output=True, text=True)
        if result.stderr.strip() and not silent:
            print(f"错误: {result.stderr.strip()}")
    else:
        result = subprocess.run(command, shell=True, check=False)

    if not silent:
        if result.returncode == 0:
            print(f"✅ 完成: {description}")
        else:
            print(f"⚠️ 失败: {description} (返回码: {result.returncode})")
        print("-" * 50)
        time.sleep(1)

    return result

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    print("📥 安装系统配置优化...")

    username, user_home, _, _ = get_user_info()

    download_dir = f"/media/{username}/download"
    downloads_link = os.path.join(user_home, "下载")

    run_command_sync(f"sudo mkdir -p {download_dir}", "创建下载目录", capture_output=True)
    run_command_sync(f"sudo chown {username}:{username} {download_dir}", "设置目录权限", capture_output=True)

    if os.path.exists(downloads_link) and not os.path.islink(downloads_link):
        backup_dir = "/tmp/downloads_backup"
        run_command_sync(f"mkdir -p {backup_dir}", "创建备份目录", capture_output=True)
        run_command_sync(f"cp -a {downloads_link}/* {backup_dir}/", "备份原下载目录", capture_output=True)
        run_command_sync(f"cp -a {backup_dir}/* {download_dir}/", "恢复下载文件", capture_output=True)
        run_command_sync(f"rm -rf {downloads_link}", "删除原下载目录", capture_output=True)

    if os.path.islink(downloads_link):
        run_command_sync(f"rm {downloads_link}", "删除旧符号链接", capture_output=True)

    run_command_sync(f"ln -sf {download_dir} {downloads_link}", "创建下载目录符号链接", capture_output=True)

    desktop_env = os.environ.get('XDG_CURRENT_DESKTOP', '').lower()
    if 'gnome' in desktop_env and shutil.which('gsettings'):
        result = run_command_sync("gsettings list-schemas | grep -E 'ubuntu-dock|dash-to-dock' | head -1",
                              "查找dock配置", capture_output=True, silent=True)
        if result.stdout.strip():
            dock_schema = result.stdout.strip()
            run_command_sync(f"gsettings set {dock_schema} show-mounts false", "隐藏挂载点", capture_output=True)
            run_command_sync(f"gsettings set {dock_schema} show-trash false", "隐藏回收站", capture_output=True)

        apps_to_add = ['google-chrome.desktop', 'firefox.desktop', 'code.desktop',
                      'vmware-workstation.desktop', 'teamviewer.desktop', 'siyuan.desktop']

        result = run_command_sync("gsettings get org.gnome.shell favorite-apps",
                              "获取收藏应用", capture_output=True, silent=True)
        current_apps = result.stdout.strip() if result.returncode == 0 else "[]"

        new_apps = []
        for app in apps_to_add:
            app_path1 = f"/usr/share/applications/{app}"
            app_path2 = os.path.join(user_home, f".local/share/applications/{app}")
            if (os.path.exists(app_path1) or os.path.exists(app_path2)) and app not in current_apps:
                new_apps.append(f"'{app}'")

        if new_apps:
            current_clean = current_apps.strip('[]').strip()
            if current_clean:
                final_apps = f"[{current_clean}, {', '.join(new_apps)}]"
            else:
                final_apps = f"[{', '.join(new_apps)}]"
            run_command_sync(f"gsettings set org.gnome.shell favorite-apps \"{final_apps}\"",
                          "设置收藏应用", capture_output=True)

    print("✅ 系统配置优化安装完成")
    return True

def uninstall():
    print("🔄 开始卸载系统配置优化...")

    _, user_home, _, _ = get_user_info()
    downloads_link = os.path.join(user_home, "下载")

    if os.path.islink(downloads_link):
        run_command_sync(f"rm {downloads_link}", "删除下载目录符号链接", capture_output=True)
        run_command_sync(f"mkdir -p {downloads_link}", "恢复原下载目录", capture_output=True)

    desktop_env = os.environ.get('XDG_CURRENT_DESKTOP', '').lower()
    if 'gnome' in desktop_env and shutil.which('gsettings'):
        result = run_command_sync("gsettings list-schemas | grep -E 'ubuntu-dock|dash-to-dock' | head -1",
                              "查找dock配置", capture_output=True, silent=True)
        if result.stdout.strip():
            dock_schema = result.stdout.strip()
            run_command_sync(f"gsettings set {dock_schema} show-mounts true", "显示挂载点", capture_output=True)
            run_command_sync(f"gsettings set {dock_schema} show-trash true", "显示回收站", capture_output=True)

        run_command_sync("gsettings reset org.gnome.shell favorite-apps", "重置收藏应用", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    _, user_home, _, _ = get_user_info()
    downloads_link = os.path.join(user_home, "下载")

    if os.path.islink(downloads_link):
        try:
            target = os.readlink(downloads_link)
            if os.path.exists(target):
                print('🟢 状态: 已安装')
                return 0
            else:
                print('🟡 状态: 已安装')
                return 0
        except:
            print('🟡 状态: 已安装')
            return 0
    else:
        print('🔴 状态: 未安装')
        return 2

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)