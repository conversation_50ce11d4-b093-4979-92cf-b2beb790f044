#!/usr/bin/env python3
"""
通用函数库
包含所有软件包安装脚本中的通用函数
"""

import os
import subprocess
import pwd
import time
import getpass

# 全局标志，避免重复禁用自动更新服务
_auto_update_disabled = False


def get_user_info():
    """
    获取用户信息
    返回: (用户名, 主目录, UID, GID)
    """
    sudo_user = os.environ.get('SUDO_USER')
    if sudo_user:
        try:
            user_info = pwd.getpwnam(sudo_user)
            return user_info.pw_name, user_info.pw_dir, user_info.pw_uid, user_info.pw_gid
        except KeyError:
            pass

    # 如果没有SUDO_USER，使用当前用户信息
    try:
        current_user = os.environ.get('USER') or getpass.getuser()
        user_info = pwd.getpwnam(current_user)
        return user_info.pw_name, user_info.pw_dir, user_info.pw_uid, user_info.pw_gid
    except (KeyError, ImportError):
        # 最后的备用方案
        return "mxhou", "/home/<USER>", 1000, 1000


def run_command_sync(command, description="", capture_output=False, silent=False):
    """
    同步执行命令

    Args:
        command: 要执行的命令
        description: 命令描述
        capture_output: 是否捕获输出
        silent: 是否静默执行

    Returns:
        subprocess.CompletedProcess: 命令执行结果
    """
    if not silent:
        print("-" * 50)
        if description:
            print(f"🔄 {description}")
        print(f"执行: {command}")

    if capture_output:
        result = subprocess.run(command, shell=True, check=False, capture_output=True, text=True)
        if result.stderr.strip() and not silent:
            print(f"错误: {result.stderr.strip()}")
    else:
        result = subprocess.run(command, shell=True, check=False)

    if not silent:
        if result.returncode == 0:
            print(f"✅ 完成: {description}")
        else:
            print(f"⚠️ 失败: {description} (返回码: {result.returncode})")
        print("-" * 50)
        time.sleep(1)

    return result


def wait_for_apt(max_wait_seconds=60):
    """
    等待apt进程完成，带超时和强制结束机制
    检查并等待所有apt相关的锁文件释放

    Args:
        max_wait_seconds: 最大等待时间（秒），默认60秒
    """
    global _auto_update_disabled

    print("🔄 检查是否有其他apt进程正在运行...")

    # 只在第一次调用时执行彻底的禁用操作
    if not _auto_update_disabled:
        print("🔧 停止并禁用自动更新服务...")
        services_to_disable = [
            "unattended-upgrades",
            "apt-daily.timer",
            "apt-daily-upgrade.timer",
            "apt-daily.service",
            "apt-daily-upgrade.service",
            "update-notifier-download.timer",
            "update-notifier-motd.timer"
        ]

        print("   🔄 强制终止相关进程...")
        kill_commands = [
            "sudo pkill -9 -f unattended-upgrade 2>/dev/null || true",
            "sudo pkill -9 -f apt-daily 2>/dev/null || true",
            "sudo pkill -9 -f apt-get 2>/dev/null || true",
            "sudo pkill -9 -f dpkg 2>/dev/null || true",
            "sudo pkill -9 -f update-notifier 2>/dev/null || true",
            "sudo pkill -9 -f update-manager 2>/dev/null || true"
        ]
        for cmd in kill_commands:
            subprocess.run(cmd, shell=True, check=False, capture_output=True)

        for service in services_to_disable:
            # 停止服务
            subprocess.run(f"sudo systemctl stop {service} 2>/dev/null || true",
                          shell=True, check=False, capture_output=True)
            # 禁用服务，防止重新启动
            subprocess.run(f"sudo systemctl disable {service} 2>/dev/null || true",
                          shell=True, check=False, capture_output=True)
            # 屏蔽服务，防止被其他服务启动
            subprocess.run(f"sudo systemctl mask {service} 2>/dev/null || true",
                          shell=True, check=False, capture_output=True)

        print("   🔄 清理锁文件...")
        lock_files = [
            "/var/lib/dpkg/lock",
            "/var/lib/dpkg/lock-frontend",
            "/var/lib/apt/lists/lock",
            "/var/cache/apt/archives/lock"
        ]
        for lock_file in lock_files:
            subprocess.run(f"sudo rm -f {lock_file} 2>/dev/null || true",
                          shell=True, check=False, capture_output=True)

        print("   🔄 修复dpkg状态...")
        subprocess.run("sudo dpkg --configure -a 2>/dev/null || true",
                      shell=True, check=False, capture_output=True)

        print("   🔄 检查锁状态...")
        has_lock = False
        for lock_file in lock_files:
            result = subprocess.run(f"sudo fuser {lock_file} >/dev/null 2>&1", shell=True)
            if result.returncode == 0:
                has_lock = True
                break

        if has_lock:
            print("   ⚠️ 发现残留锁，立即强制解决...")
            _force_kill_apt_processes()

        _auto_update_disabled = True
        print("✅ 自动更新服务已彻底禁用")
    else:
        subprocess.run("sudo pkill -9 -f unattended-upgrade 2>/dev/null || true",
                      shell=True, check=False, capture_output=True)
        subprocess.run("sudo rm -f /var/lib/dpkg/lock* /var/lib/apt/lists/lock /var/cache/apt/archives/lock 2>/dev/null || true",
                      shell=True, check=False, capture_output=True)

    start_time = time.time()
    wait_count = 0

    actual_timeout = 2 if _auto_update_disabled else max_wait_seconds

    while True:
        # 检查是否超时
        elapsed_time = time.time() - start_time
        if elapsed_time > actual_timeout:
            print(f"⚠️ 等待超时（{actual_timeout}秒），强制解决apt锁问题...")
            _force_kill_apt_processes()
            break

        # 检查各种锁文件
        lock_files = [
            "/var/lib/dpkg/lock",
            "/var/lib/apt/lists/lock",
            "/var/lib/dpkg/lock-frontend"
        ]

        locked = False
        for lock_file in lock_files:
            # 首先检查锁文件是否存在
            if os.path.exists(lock_file):
                # 如果存在，检查是否被占用
                result = subprocess.run(f"sudo fuser {lock_file} >/dev/null 2>&1", shell=True)
                if result.returncode == 0:
                    locked = True
                    break

        # 检查apt进程（排除非相关进程）
        result = subprocess.run("pgrep -f 'apt-get|dpkg|unattended-upgrade' 2>/dev/null", shell=True, capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            # 进一步验证是否是真正的apt/dpkg进程
            current_pid = os.getpid()
            for pid in result.stdout.strip().split('\n'):
                if pid.strip() and int(pid.strip()) != current_pid:
                    cmd_result = subprocess.run(f"ps -p {pid.strip()} -o comm= 2>/dev/null",
                                              shell=True, capture_output=True, text=True)
                    if cmd_result.returncode == 0:
                        comm = cmd_result.stdout.strip()
                        if comm in ['apt-get', 'dpkg', 'unattended-upgr']:
                            locked = True
                            break

        if not locked:
            print("✅ 没有其他apt进程在运行")
            break

        wait_count += 1
        if wait_count <= 5:
            print("⏳ 等待其他apt进程完成...")
        elif wait_count == 10:
            print("⏳ 仍在等待apt进程完成，如果长时间无响应将自动强制结束...")
        elif wait_count % 10 == 0:
            remaining_time = actual_timeout - elapsed_time
            print(f"⏳ 继续等待apt进程完成... (剩余 {remaining_time:.0f} 秒)")

        time.sleep(3)


def _force_kill_apt_processes():
    """
    强制结束apt相关进程，避免误杀自己
    """
    print("🔧 强制解决apt锁问题...")

    # 获取当前进程信息（用于避免误杀）
    # current_pid = os.getpid()
    # current_ppid = os.getppid()

    # 1. 直接终止常见的更新进程
    print("   🔄 终止更新进程...")
    update_commands = [
        "sudo pkill -9 -f unattended-upgrade 2>/dev/null || true",
        "sudo pkill -9 -f apt-get 2>/dev/null || true",
        "sudo pkill -9 -f dpkg 2>/dev/null || true"
    ]

    for cmd in update_commands:
        try:
            subprocess.run(cmd, shell=True, check=False, timeout=3, capture_output=True)
        except:
            pass

    # 2. 强制删除锁文件
    print("   🔄 删除锁文件...")
    lock_commands = [
        "sudo rm -f /var/lib/dpkg/lock 2>/dev/null || true",
        "sudo rm -f /var/lib/dpkg/lock-frontend 2>/dev/null || true",
        "sudo rm -f /var/lib/apt/lists/lock 2>/dev/null || true",
        "sudo rm -f /var/cache/apt/archives/lock 2>/dev/null || true"
    ]

    for cmd in lock_commands:
        try:
            subprocess.run(cmd, shell=True, check=False, timeout=2, capture_output=True)
        except:
            pass

    print("   ✅ apt锁问题解决完成！")


def detect_virtual_machine():
    """
    检测是否运行在虚拟机中

    Returns:
        str: 虚拟机类型 ("VMware", "VirtualBox", "QEMU/KVM", "Hyper-V") 或 None
    """
    # 首先检查模块，这是最可靠的方法
    try:
        result = subprocess.run("lsmod", shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            output = result.stdout.lower()
            if "vmw_" in output or "vmware" in output:
                return "VMware"
            elif "vboxguest" in output or "vbox" in output:
                return "VirtualBox"
            elif "virtio" in output:
                return "QEMU/KVM"
            elif "hv_" in output or "hyperv" in output:
                return "Hyper-V"
    except:
        pass

    # 检查DMI信息
    try:
        result = subprocess.run("sudo dmidecode -s system-manufacturer",
                              shell=True, capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            manufacturer = result.stdout.strip().lower()
            if "vmware" in manufacturer:
                return "VMware"
            elif "innotek" in manufacturer or "oracle" in manufacturer:
                return "VirtualBox"
            elif "qemu" in manufacturer:
                return "QEMU/KVM"
            elif "microsoft" in manufacturer:
                return "Hyper-V"
    except:
        pass

    # 检查CPU信息
    try:
        with open("/proc/cpuinfo", "r") as f:
            cpuinfo = f.read().lower()
            if "vmware" in cpuinfo:
                return "VMware"
            elif "virtualbox" in cpuinfo:
                return "VirtualBox"
            elif "qemu" in cpuinfo or "kvm" in cpuinfo:
                return "QEMU/KVM"
    except:
        pass

    return None


def get_system_info():
    """
    获取系统信息

    Returns:
        dict: 包含系统信息的字典
    """
    info = {
        'os_name': None,
        'os_version': None,
        'architecture': None,
        'virtual_machine': None
    }

    try:
        # 获取操作系统信息
        result = subprocess.run("lsb_release -d", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            info['os_name'] = result.stdout.split('\t')[1].strip()
    except:
        pass

    try:
        # 获取架构信息
        result = subprocess.run("uname -m", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            info['architecture'] = result.stdout.strip()
    except:
        pass

    # 检测虚拟机
    info['virtual_machine'] = detect_virtual_machine()

    return info


def check_internet_connection(timeout=5):
    """
    检查网络连接

    Args:
        timeout: 超时时间（秒）

    Returns:
        bool: 是否有网络连接
    """
    try:
        result = subprocess.run(
            ["ping", "-c", "1", "-W", str(timeout), "*******"],
            capture_output=True,
            timeout=timeout + 2
        )
        return result.returncode == 0
    except:
        return False


def is_package_installed(package_name):
    """
    检查软件包是否已安装

    Args:
        package_name: 软件包名称

    Returns:
        bool: 是否已安装
    """
    try:
        result = subprocess.run(
            ["dpkg-query", "-W", "-f=${Status}", package_name],
            capture_output=True,
            text=True
        )
        return "install ok installed" in result.stdout
    except:
        return False


def get_available_space(path="/"):
    """
    获取指定路径的可用空间（GB）

    Args:
        path: 路径，默认为根目录

    Returns:
        float: 可用空间（GB）
    """
    try:
        statvfs = os.statvfs(path)
        available_bytes = statvfs.f_frsize * statvfs.f_bavail
        return available_bytes / (1024 ** 3)  # 转换为GB
    except:
        return 0.0
