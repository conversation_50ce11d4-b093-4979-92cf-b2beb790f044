#!/usr/bin/env python3
"""
Docker镜像推拉管理
支持平台: Ubuntu/Debian
架构: x86_64
"""

import os
import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import run_command_sync
from packages.common.gitea_package_manager import GiteaPackageManager

def get_all_docker_images():
    """获取当前主机上的所有Docker镜像"""
    result = run_command_sync("docker images --format '{{.Repository}}:{{.Tag}}\t{{.ID}}\t{{.Size}}'",
                             "获取Docker镜像列表", capture_output=True, silent=True)

    if result.returncode != 0:
        print("❌ 获取Docker镜像失败，请确保Docker已安装并运行")
        return []

    images = []
    lines = result.stdout.strip().split('\n')

    for line in lines:
        if line.strip():
            parts = line.split('\t')
            if len(parts) >= 3:
                repo_tag = parts[0].strip()
                image_id = parts[1].strip()
                size = parts[2].strip()

                if ':' in repo_tag and not repo_tag.startswith('<none>'):
                    repo, tag = repo_tag.rsplit(':', 1)
                    images.append({
                        'repository': repo,
                        'tag': tag,
                        'id': image_id,
                        'size': size,
                        'full_name': repo_tag
                    })

    return images

def get_gitea_docker_images():
    """获取Gitea上已有的Docker镜像"""
    try:
        manager = GiteaPackageManager()
        packages = manager.list_packages('container')  # Docker镜像在Gitea中的类型是container

        images = []
        for pkg in packages:
            name = pkg.get('name', '')
            version = pkg.get('version', 'latest')
            images.append({
                'name': name,
                'version': version,
                'full_name': f"{name}:{version}"
            })

        return images
    except Exception as e:
        print(f"❌ 获取Gitea镜像列表失败: {e}")
        return []

def delete_local_images(images):
    """删除本地Docker镜像"""
    if not images:
        print("❌ 没有镜像可删除")
        return False  # 返回主菜单

    print(f"\n🗑️ 删除本地Docker镜像")
    print("-" * 60)
    print(f"{'序号':<4} {'镜像名称':<40} {'大小':<10}")
    print("-" * 60)

    for i, img in enumerate(images, 1):
        print(f"{i:<4} {img['full_name']:<40} {img['size']:<10}")

    print("-" * 60)
    print("请选择要删除的镜像 (输入序号，多个用逗号分隔，或输入 'all' 删除全部，'q' 返回):")

    try:
        choice = input("选择: ").strip()

        if choice.lower() in ['q', 'quit', 'exit']:
            print("✅ 返回主菜单")
            return False  # 返回主菜单
        elif choice.lower() == 'all':
            selected_images = images
        else:
            indices = [int(x.strip()) for x in choice.split(',') if x.strip().isdigit()]
            selected_images = [images[i-1] for i in indices if 1 <= i <= len(images)]

        if not selected_images:
            print("❌ 没有选择任何镜像")
            return False  # 返回主菜单

        print(f"\n🗑️ 开始删除 {len(selected_images)} 个本地镜像...")

        success_count = 0
        for img in selected_images:
            print(f"\n🔄 删除镜像: {img['full_name']}")

            result = run_command_sync(f"docker rmi {img['full_name']}",
                                    f"删除镜像 {img['full_name']}", capture_output=True)

            if result.returncode == 0:
                success_count += 1
                print(f"✅ {img['full_name']} 删除成功")
            else:
                print(f"❌ {img['full_name']} 删除失败: {result.stderr.strip()}")

        print(f"\n🎉 删除完成! 成功: {success_count}/{len(selected_images)}")
        print("📋 返回主菜单...")
        return False  # 返回主菜单

    except KeyboardInterrupt:
        print("\n❌ 用户取消操作")
        return False  # 返回主菜单
    except Exception as e:
        print(f"❌ 删除过程中出错: {e}")
        return False  # 返回主菜单

def delete_gitea_images(images):
    """删除Gitea上的Docker镜像"""
    if not images:
        print("❌ 没有镜像可删除")
        return False  # 返回主菜单

    print(f"\n🗑️ 删除Gitea Docker镜像")
    print("-" * 60)
    print(f"{'序号':<4} {'镜像名称':<50}")
    print("-" * 60)

    for i, img in enumerate(images, 1):
        print(f"{i:<4} {img['full_name']:<50}")

    print("-" * 60)
    print("请选择要删除的镜像 (输入序号，多个用逗号分隔，或输入 'all' 删除全部，'q' 返回):")

    try:
        choice = input("选择: ").strip()

        if choice.lower() in ['q', 'quit', 'exit']:
            print("✅ 返回主菜单")
            return False  # 返回主菜单
        elif choice.lower() == 'all':
            selected_images = images
        else:
            indices = [int(x.strip()) for x in choice.split(',') if x.strip().isdigit()]
            selected_images = [images[i-1] for i in indices if 1 <= i <= len(images)]

        if not selected_images:
            print("❌ 没有选择任何镜像")
            return False  # 返回主菜单

        print(f"\n🗑️ 开始删除 {len(selected_images)} 个Gitea镜像...")

        manager = GiteaPackageManager()
        success_count = 0

        for img in selected_images:
            print(f"\n🔄 删除镜像: {img['full_name']}")

            success = manager.delete_package('container', img['name'], img['version'])

            if success:
                success_count += 1
                print(f"✅ {img['full_name']} 删除成功")
            else:
                print(f"❌ {img['full_name']} 删除失败")

        print(f"\n🎉 删除完成! 成功: {success_count}/{len(selected_images)}")
        print("📋 返回主菜单...")
        return False  # 返回主菜单

    except KeyboardInterrupt:
        print("\n❌ 用户取消操作")
        return False  # 返回主菜单
    except Exception as e:
        print(f"❌ 删除过程中出错: {e}")
        return False  # 返回主菜单

def delete_all_gitea_packages():
    """删除所有Gitea包（调用内置的包管理功能）"""
    print("\n🗑️ Gitea包管理 - 删除所有包")
    print("=" * 50)

    try:
        from packages.common.gitea_package_manager import quick_demo
        print("正在启动Gitea包管理界面...")
        quick_demo()
        print("📋 返回主菜单...")
        return False  # 返回主菜单
    except Exception as e:
        print(f"❌ 启动包管理界面失败: {e}")
        return False  # 返回主菜单

def interactive_push_images():
    """交互式推送镜像到Gitea"""
    while True:
        print("\n🐳 Docker镜像推送到Gitea包管理")
        print("=" * 50)

        images = get_all_docker_images()
        if not images:
            print("📭 没有找到任何Docker镜像")
            print("✅ 推送操作完成（无镜像需要推送）")
            return True

        print(f"✅ 找到 {len(images)} 个Docker镜像:")
        print("-" * 60)
        print(f"{'序号':<4} {'镜像名称':<40} {'大小':<10}")
        print("-" * 60)

        for i, img in enumerate(images, 1):
            print(f"{i:<4} {img['full_name']:<40} {img['size']:<10}")

        print("-" * 60)
        print("请选择要推送的镜像 (输入序号，多个用逗号分隔，或输入 'all' 推送全部)")
        print("或输入 'delete' 删除本地镜像，'q' 退出:")

        try:
            choice = input("选择: ").strip()

            if choice.lower() in ['q', 'quit', 'exit']:
                print("✅ 用户退出，推送操作取消")
                return True
            elif choice.lower() in ['delete', 'del', 'd']:
                # 删除操作，如果返回False则继续循环
                if not delete_local_images(images):
                    continue
                else:
                    return True
            elif choice.lower() == 'all':
                selected_images = images
            else:
                indices = [int(x.strip()) for x in choice.split(',') if x.strip().isdigit()]
                selected_images = [images[i-1] for i in indices if 1 <= i <= len(images)]

            if not selected_images:
                print("❌ 没有选择任何镜像")
                print("✅ 推送操作完成（无镜像被选择）")
                continue

            print(f"\n📤 开始推送 {len(selected_images)} 个镜像...")

            manager = GiteaPackageManager()
            success_count = 0

            for img in selected_images:
                print(f"\n🔄 推送镜像: {img['full_name']}")

                repo = img['repository']
                tag = img['tag']

                success = manager.upload('docker', repo, tag, local_image=img['full_name'])

                if success:
                    success_count += 1
                    print(f"✅ {img['full_name']} 推送成功")
                else:
                    print(f"❌ {img['full_name']} 推送失败")

            print(f"\n🎉 推送完成! 成功: {success_count}/{len(selected_images)}")
            return success_count > 0

        except KeyboardInterrupt:
            print("\n❌ 用户取消操作")
            return False
        except Exception as e:
            print(f"❌ 推送过程中出错: {e}")
            return False

def interactive_pull_images():
    """交互式从Gitea拉取镜像"""
    while True:
        print("\n🐳 从Gitea包管理拉取Docker镜像")
        print("=" * 50)

        images = get_gitea_docker_images()
        if not images:
            print("📭 Gitea上没有找到任何Docker镜像")
            print("✅ 拉取操作完成（无镜像需要拉取）")
            return True

        print(f"✅ 找到 {len(images)} 个Docker镜像:")
        print("-" * 60)
        print(f"{'序号':<4} {'镜像名称':<50}")
        print("-" * 60)

        for i, img in enumerate(images, 1):
            print(f"{i:<4} {img['full_name']:<50}")

        print("-" * 60)
        print("请选择要拉取的镜像 (输入序号，多个用逗号分隔，或输入 'all' 拉取全部)")
        print("或输入 'delete' 删除Gitea镜像，'packages' 管理所有Gitea包，'q' 退出:")

        try:
            choice = input("选择: ").strip()

            if choice.lower() in ['q', 'quit', 'exit']:
                print("✅ 用户退出，拉取操作取消")
                return True
            elif choice.lower() in ['delete', 'del', 'd']:
                # 删除操作，如果返回False则继续循环
                if not delete_gitea_images(images):
                    continue
                else:
                    return True
            elif choice.lower() in ['packages', 'pkg', 'p']:
                # 包管理操作，如果返回False则继续循环
                if not delete_all_gitea_packages():
                    continue
                else:
                    return True
            elif choice.lower() == 'all':
                selected_images = images
            else:
                indices = [int(x.strip()) for x in choice.split(',') if x.strip().isdigit()]
                selected_images = [images[i-1] for i in indices if 1 <= i <= len(images)]

            if not selected_images:
                print("❌ 没有选择任何镜像")
                print("✅ 拉取操作完成（无镜像被选择）")
                continue

            print(f"\n📥 开始拉取 {len(selected_images)} 个镜像...")

            manager = GiteaPackageManager()
            success_count = 0

            for img in selected_images:
                print(f"\n🔄 拉取镜像: {img['full_name']}")

                success = manager.download('docker', img['name'], img['version'])

                if success:
                    success_count += 1
                    print(f"✅ {img['full_name']} 拉取成功")
                else:
                    print(f"❌ {img['full_name']} 拉取失败")

            print(f"\n🎉 拉取完成! 成功: {success_count}/{len(selected_images)}")
            return success_count > 0

        except KeyboardInterrupt:
            print("\n❌ 用户取消操作")
            return False
        except Exception as e:
            print(f"❌ 拉取过程中出错: {e}")
            return False

def install():
    """安装 - 推送镜像"""
    return interactive_push_images()

def uninstall():
    """卸载 - 拉取镜像"""
    return interactive_pull_images()

def check_status():
    """检查状态"""
    result = run_command_sync("docker --version", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: Docker已安装')
        return 0
    else:
        print('🔴 状态: Docker未安装')
        return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
