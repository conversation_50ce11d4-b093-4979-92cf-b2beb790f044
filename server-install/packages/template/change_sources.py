#!/usr/bin/env python3
"""
更换为国内源
支持平台: Ubuntu/Debian
"""

import os
import sys
from datetime import datetime

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import run_command_sync

def get_system_info():
    """获取系统信息"""
    try:
        with open('/etc/os-release', 'r') as f:
            content = f.read()

        if 'Ubuntu' in content:
            version_line = [line for line in content.split('\n') if 'VERSION_ID=' in line][0]
            version = version_line.split('"')[1]

            version_map = {
                "20.04": "focal",
                "22.04": "jammy",
                "24.04": "noble"
            }
            codename = version_map.get(version, "jammy")
            return "ubuntu", codename

        elif 'Debian' in content:
            version_line = [line for line in content.split('\n') if 'VERSION_ID=' in line][0]
            version = version_line.split('"')[1]

            version_map = {
                "10": "buster",
                "11": "bullseye",
                "12": "bookworm"
            }
            codename = version_map.get(version, "bullseye")
            return "debian", codename

    except Exception:
        pass

    return None, None

def install():
    status_code = check_status()
    if status_code == 0:
        return True

    print('🚀 开始更换为国内源...')

    os_type, codename = get_system_info()
    if not os_type:
        print('❌ 不支持的系统')
        return False

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"/etc/apt/sources.list.backup.{timestamp}"

    run_command_sync(f"sudo cp /etc/apt/sources.list {backup_file}", "备份原始源文件")

    if os_type == "ubuntu":
        sources_content = f"""deb https://mirrors.aliyun.com/ubuntu/ {codename} main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ {codename}-security main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ {codename}-updates main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ {codename}-backports main restricted universe multiverse"""
    else:  # debian
        sources_content = f"""deb https://mirrors.aliyun.com/debian/ {codename} main non-free contrib
deb https://mirrors.aliyun.com/debian-security/ {codename}-security main
deb https://mirrors.aliyun.com/debian/ {codename}-updates main non-free contrib
deb https://mirrors.aliyun.com/debian/ {codename}-backports main non-free contrib"""

    temp_file = "/tmp/sources.list.new"
    with open(temp_file, 'w') as f:
        f.write(sources_content)

    run_command_sync(f"sudo cp {temp_file} /etc/apt/sources.list", "写入新源配置")
    run_command_sync(f"rm -f {temp_file}", "清理临时文件", capture_output=True)
    run_command_sync("sudo apt update", "更新软件包列表", capture_output=True)

    print('✅ 已更换为阿里云源')
    return True

def uninstall():
    print('🔄 恢复原始软件源...')

    result = run_command_sync("ls -t /etc/apt/sources.list.backup.* 2>/dev/null | head -1",
                             "查找备份文件", capture_output=True, silent=True)

    if result.returncode != 0 or not result.stdout.strip():
        print('❌ 未找到备份文件')
        return False

    backup_file = result.stdout.strip()
    run_command_sync(f"sudo cp {backup_file} /etc/apt/sources.list", "恢复原始源文件")
    run_command_sync("sudo apt update", "更新软件包列表", capture_output=True)

    print('✅ 已恢复原始软件源')
    return True

def check_status():
    try:
        with open('/etc/apt/sources.list', 'r') as f:
            content = f.read()

        if 'mirrors.aliyun.com' in content:
            print('🟢 状态: 已安装')
            return 0
        else:
            print('🔴 状态: 未安装')
            return 2
    except Exception:
        print('🔴 状态: 未安装')
        return 2

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
