#!/usr/bin/env python3
"""
Clash Nyanpasu 网络代理客户端
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync, detect_virtual_machine

from packages.common.package_downloader import download_package

def setup_vm_environment(vm_type=None):
    """为虚拟机环境设置优化的环境变量"""
    vm_env_file = "/etc/environment.d/99-clash-vm.conf"
    vm_env_content = """# Clash Nyanpasu 虚拟机优化配置
LIBGL_ALWAYS_SOFTWARE=1
GALLIUM_DRIVER=llvmpipe
MESA_GL_VERSION_OVERRIDE=3.3
QT_X11_NO_MITSHM=1
_JAVA_AWT_WM_NONREPARENTING=1
WEBKIT_DISABLE_COMPOSITING_MODE=1
ELECTRON_DISABLE_GPU=1
"""

    try:
        os.makedirs("/etc/environment.d", exist_ok=True)
        with open(vm_env_file, 'w') as f:
            f.write(vm_env_content)
        if vm_type:
            print(f"✅ 已为{vm_type}虚拟机设置环境优化配置")
        else:
            print("✅ 已设置虚拟机环境优化配置")
        return True
    except PermissionError:
        print("⚠️ 需要root权限设置环境变量，建议重启后生效")
        # 尝试使用sudo命令设置
        try:
            cmd = f'sudo sh -c \'mkdir -p /etc/environment.d && echo "{vm_env_content.strip()}" > {vm_env_file}\''
            result = subprocess.run(cmd, shell=True, check=False, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 已通过sudo设置环境变量")
                return True
        except:
            pass

        print("📝 请手动执行以下命令设置环境变量：")
        print(f"sudo mkdir -p /etc/environment.d")
        print(f"sudo tee {vm_env_file} << 'EOF'")
        print(vm_env_content.strip())
        print("EOF")
        return False

def create_vm_launcher(target_path, user_home, vm_type=None):
    """创建虚拟机优化启动器"""
    launcher_dir = os.path.join(user_home, ".local/bin")
    os.makedirs(launcher_dir, exist_ok=True)

    launcher_script = os.path.join(launcher_dir, "clash-nyanpasu-vm")
    launcher_content = f"""#!/bin/bash
# Clash Nyanpasu 虚拟机优化启动脚本
# 虚拟机类型: {vm_type or 'Generic'}

# 设置环境变量
export LIBGL_ALWAYS_SOFTWARE=1
export GALLIUM_DRIVER=llvmpipe
export MESA_GL_VERSION_OVERRIDE=3.3
export QT_X11_NO_MITSHM=1
export _JAVA_AWT_WM_NONREPARENTING=1
export WEBKIT_DISABLE_COMPOSITING_MODE=1
export ELECTRON_DISABLE_GPU=1

echo "🚀 启动Clash Nyanpasu (虚拟机优化版本)..."
echo "🖥️ 虚拟机类型: {vm_type or 'Generic'}"
echo "📊 使用软件渲染模式"

# 启动应用
exec "{target_path}" \\
    --disable-gpu \\
    --disable-software-rasterizer \\
    --disable-gpu-compositing \\
    --disable-dev-shm-usage \\
    --no-sandbox \\
    --disable-web-security \\
    --disable-features=VizDisplayCompositor \\
    "$@"
"""

    try:
        with open(launcher_script, 'w') as f:
            f.write(launcher_content)
        os.chmod(launcher_script, 0o755)
        print(f"✅ 创建虚拟机优化启动器: {launcher_script}")
        return launcher_script
    except Exception as e:
        print(f"⚠️ 创建启动器失败: {e}")
        return None

def install_vm_dependencies(vm_type):
    """安装虚拟机相关依赖"""
    print(f"📦 为{vm_type}虚拟机安装依赖...")

    # 基础图形依赖
    base_deps = [
        "mesa-utils", "libgl1-mesa-glx", "libgl1-mesa-dri",
        "libgtk-3-0", "libwebkit2gtk-4.0-37", "libayatana-appindicator3-1",
        "libxss1", "libgconf-2-4", "libnss3", "libgdk-pixbuf2.0-0"
    ]

    # 虚拟机特定依赖
    vm_deps = []
    if vm_type == "VMware":
        vm_deps = ["open-vm-tools", "open-vm-tools-desktop"]
    elif vm_type == "VirtualBox":
        vm_deps = ["virtualbox-guest-additions-iso", "virtualbox-guest-utils"]
    elif vm_type == "QEMU/KVM":
        vm_deps = ["qemu-guest-agent"]

    all_deps = base_deps + vm_deps

    try:
        # 更新软件包列表
        run_command_sync("sudo apt update", "更新软件包列表", capture_output=True, silent=True)

        # 安装依赖
        deps_str = " ".join(all_deps)
        result = run_command_sync(f"sudo apt install -y {deps_str}",
                                f"安装{vm_type}依赖", capture_output=True, silent=True)

        if result.returncode == 0:
            print(f"✅ {vm_type}依赖安装完成")
            return True
        else:
            print(f"⚠️ 部分依赖安装失败，但不影响基本功能")
            return False
    except Exception as e:
        print(f"⚠️ 依赖安装过程中出现错误: {e}")
        return False
def get_latest_version():
    try:
        response = requests.get(
            "https://api.github.com/repos/LibNyanpasu/clash-nyanpasu/releases/latest",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            return data.get('tag_name', '').lstrip('v')
    except:
        pass
    return "1.6.1"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    filename = f"clash-nyanpasu_{version}_amd64.AppImage"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载Clash Nyanpasu...")
        download_url = f"https://github.com/LibNyanpasu/clash-nyanpasu/releases/download/v{version}/{filename}"

        response = requests.get(download_url, stream=True, timeout=60)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

        print()

        if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    downloaded_file, _ = download_package(
        package_name="clash-nyanpasu",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    _, user_home, uid, gid = get_user_info()
    app_dir = os.path.join(user_home, "Applications")
    os.makedirs(app_dir, exist_ok=True)

    target_path = os.path.join(app_dir, "clash-nyanpasu.AppImage")

    shutil.copy2(downloaded_file, target_path)
    os.chmod(target_path, 0o755)

    try:
        os.chown(target_path, uid, gid)
    except PermissionError:
        pass

    desktop_dir = os.path.join(user_home, ".local/share/applications")
    os.makedirs(desktop_dir, exist_ok=True)

    # 检测虚拟机环境并添加相应参数
    vm_type = detect_virtual_machine()
    vm_args = ""
    vm_launcher = None

    if vm_type:
        vm_args = " --disable-gpu --disable-software-rasterizer --disable-gpu-compositing --disable-dev-shm-usage --no-sandbox"
        print(f"🔍 检测到{vm_type}虚拟机环境，正在优化配置...")

        # 安装虚拟机依赖
        install_vm_dependencies(vm_type)

        # 设置环境变量
        setup_vm_environment(vm_type)

        # 创建优化启动器
        vm_launcher = create_vm_launcher(target_path, user_home, vm_type)

        print(f"✅ {vm_type}虚拟机优化配置完成")

    desktop_file = os.path.join(desktop_dir, "clash-nyanpasu.desktop")

    # 根据是否为虚拟机环境调整桌面文件
    if vm_type:
        app_name = f"Clash Nyanpasu ({vm_type})"
        app_comment = f"Clash Nyanpasu - {vm_type}虚拟机优化版本"
        # 如果有优化启动器，优先使用
        exec_path = vm_launcher if vm_launcher else f"{target_path}{vm_args}"
    else:
        app_name = "Clash Nyanpasu"
        app_comment = "Clash Nyanpasu - A clash GUI based on tauri"
        exec_path = target_path

    desktop_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name={app_name}
Comment={app_comment}
Exec={exec_path}
Icon=clash-nyanpasu
StartupNotify=true
Categories=Network;
Terminal=false
"""

    with open(desktop_file, 'w') as f:
        f.write(desktop_content)

    os.chmod(desktop_file, 0o644)

    autostart_dir = os.path.join(user_home, ".config/autostart")
    os.makedirs(autostart_dir, exist_ok=True)

    autostart_file = os.path.join(autostart_dir, "clash-nyanpasu.desktop")

    # 自启动文件也根据虚拟机环境调整
    if vm_type and vm_launcher:
        autostart_exec = f"{vm_launcher} --minimized"
        autostart_name = f"Clash Nyanpasu ({vm_type})"
    else:
        autostart_exec = f"{target_path} --minimized{vm_args}"
        autostart_name = "Clash Nyanpasu"

    autostart_content = f"""[Desktop Entry]
Type=Application
Name={autostart_name}
Exec={autostart_exec}
Hidden=false
X-GNOME-Autostart-enabled=true
"""

    with open(autostart_file, 'w') as f:
        f.write(autostart_content)

    os.chmod(autostart_file, 0o644)

    try:
        os.chown(desktop_file, uid, gid)
        os.chown(autostart_file, uid, gid)
        os.chown(desktop_dir, uid, gid)
        os.chown(autostart_dir, uid, gid)
        os.chown(app_dir, uid, gid)
    except PermissionError:
        pass

    if vm_type:
        print("✅ Clash Nyanpasu虚拟机优化版安装完成")
        print(f"🖥️ 虚拟机类型: {vm_type}")
        if vm_launcher:
            print(f"🚀 优化启动器: {vm_launcher}")
        print("📝 建议重启虚拟机以确保所有优化生效")
        print("💡 如遇问题，请确保虚拟机已启用3D加速并分配足够显存")
    else:
        print("✅ Clash Nyanpasu安装完成")

    return True

def uninstall():
    print("🔄 开始卸载Clash Nyanpasu...")

    current_pid = os.getpid()
    current_ppid = os.getppid()

    protected_pids = {current_pid, current_ppid}

    python_result = run_command_sync("pgrep -f 'python.*clash'", capture_output=True, silent=True)
    if python_result.returncode == 0:
        for pid in python_result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    protected_pids.add(int(pid.strip()))
                except ValueError:
                    continue

    result = run_command_sync("pgrep -f 'clash-nyanpasu'", "查找Clash Nyanpasu进程", capture_output=True, silent=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    pid_num = int(pid.strip())
                    if pid_num not in protected_pids:
                        run_command_sync(f"kill {pid_num}", f"停止进程 {pid_num}", capture_output=True)
                except ValueError:
                    continue

    run_command_sync("sudo snap remove clash-nyanpasu", "检查并卸载Snap版本", capture_output=True)

    _, user_home, _, _ = get_user_info()
    app_dir = os.path.join(user_home, "Applications")
    desktop_file = os.path.join(user_home, ".local/share/applications/clash-nyanpasu.desktop")
    autostart_file = os.path.join(user_home, ".config/autostart/clash-nyanpasu.desktop")

    if os.path.exists(app_dir):
        for file in os.listdir(app_dir):
            if "clash-nyanpasu" in file and file.endswith(".AppImage"):
                file_path = os.path.join(app_dir, file)
                run_command_sync(f"rm -f '{file_path}'", f"清理应用文件 {file}", capture_output=True)

    run_command_sync(f"rm -f '{desktop_file}'", "清理桌面文件", capture_output=True)
    run_command_sync(f"rm -f '{autostart_file}'", "清理自启动文件", capture_output=True)

    # 清理虚拟机优化启动器
    vm_launcher = os.path.join(user_home, ".local/bin/clash-nyanpasu-vm")
    if os.path.exists(vm_launcher):
        run_command_sync(f"rm -f '{vm_launcher}'", "清理虚拟机启动器", capture_output=True)

    # 清理虚拟机环境配置（可选，用户可能想保留）
    vm_env_file = "/etc/environment.d/99-clash-vm.conf"
    if os.path.exists(vm_env_file):
        try:
            os.remove(vm_env_file)
            print("🧹 已清理虚拟机环境配置")
        except PermissionError:
            print("⚠️ 虚拟机环境配置需要手动清理:")
            print(f"sudo rm -f {vm_env_file}")

    config_dirs = [
        os.path.join(user_home, ".local/share/clash-nyanpasu"),
        os.path.join(user_home, ".local/share/moe.elaina.clash.nyanpasu"),
        os.path.join(user_home, ".config/clash-nyanpasu")
    ]

    for config_dir in config_dirs:
        if os.path.exists(config_dir):
            run_command_sync(f"rm -rf '{config_dir}'", f"清理配置目录 {os.path.basename(config_dir)}", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    _, user_home, _, _ = get_user_info()
    app_dir = os.path.join(user_home, "Applications")

    if os.path.exists(app_dir):
        installed_files = [f for f in os.listdir(app_dir) if "clash-nyanpasu" in f and f.endswith(".AppImage")]
        if installed_files:
            print('🟢 状态: 已安装')
            return 0

    result = run_command_sync("snap list clash-nyanpasu", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)