#!/usr/bin/env python3
"""
Docker 容器化平台
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    username, _, _, _ = get_user_info()

    run_command_sync("sudo apt-get update", "更新软件包列表")
    run_command_sync("sudo apt-get install -y curl ca-certificates gnupg lsb-release", "安装依赖包")
    run_command_sync("sudo mkdir -p /etc/apt/keyrings", "创建密钥目录")

    run_command_sync("curl -fsSL https://mirrors.aliyun.com/docker-ce/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg", "添加Docker GPG密钥", capture_output=True)

    run_command_sync('echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://mirrors.aliyun.com/docker-ce/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null', "添加Docker软件源")

    run_command_sync("sudo apt-get update", "更新软件包列表")
    run_command_sync("sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin", "安装Docker", capture_output=True)

    # 安装 Docker Compose v1 (独立版本)
    install_docker_compose_v1()

    run_command_sync("sudo systemctl enable docker", "启用Docker服务")
    run_command_sync("sudo systemctl start docker", "启动Docker服务")
    run_command_sync(f"sudo usermod -aG docker {username}", f"将用户 {username} 添加到docker组")
    run_command_sync("sudo chmod 666 /var/run/docker.sock", "设置docker.sock权限")

    configure_docker_mirrors()

    print("✅ Docker安装完成")
    return True

def install_docker_compose_v1():
    """安装 Docker Compose v1 (独立版本)"""
    print("📦 安装 Docker Compose v1...")

    # 获取最新的 Docker Compose v1 版本
    compose_version = "1.29.2"  # Docker Compose v1 的最后一个稳定版本

    # 下载 Docker Compose v1 二进制文件
    run_command_sync(
        f"sudo curl -L \"https://github.com/docker/compose/releases/download/{compose_version}/docker-compose-$(uname -s)-$(uname -m)\" -o /usr/local/bin/docker-compose",
        "下载 Docker Compose v1",
        capture_output=True
    )

    # 设置执行权限
    run_command_sync("sudo chmod +x /usr/local/bin/docker-compose", "设置 Docker Compose v1 执行权限")

    # 验证安装
    result = run_command_sync("docker-compose --version", "验证 Docker Compose v1 安装", capture_output=True)
    if result.returncode == 0:
        print("✅ Docker Compose v1 安装成功")
        print("💡 现在您可以使用:")
        print("   - docker-compose (v1 命令)")
        print("   - docker compose (v2 命令)")
    else:
        print("❌ Docker Compose v1 安装失败")

def configure_docker_mirrors():
    """配置Docker镜像加速器"""
    daemon_config = {
        "registry-mirrors": [
            "https://mirror.ccs.tencentyun.com",
            "https://hub-mirror.c.163.com",
            "https://mirror.baidubce.com"
        ],
        "insecure-registries": [
            "192.168.190.10:5000",
            "git.sanyitec.cc"
        ],
        "dns": [
            "8.8.8.8",
            "8.8.4.4",
            "223.5.5.5",
            "223.6.6.6"
        ]
    }

    import json
    config_content = json.dumps(daemon_config, indent=2, ensure_ascii=False)

    run_command_sync("sudo mkdir -p /etc/docker", "创建Docker配置目录")

    with open("/tmp/daemon.json", "w", encoding="utf-8") as f:
        f.write(config_content)

    run_command_sync("sudo cp /tmp/daemon.json /etc/docker/daemon.json", "配置Docker镜像加速器")
    run_command_sync("sudo systemctl restart docker", "重启Docker服务应用配置")
    run_command_sync("rm -f /tmp/daemon.json", "清理临时文件", capture_output=True)

def uninstall():
    print("🔄 开始卸载Docker...")

    run_command_sync("sudo systemctl stop docker", "停止Docker服务", capture_output=True)
    run_command_sync("sudo systemctl disable docker", "禁用Docker服务", capture_output=True)

    run_command_sync("sudo snap remove docker", "卸载Snap版本Docker", capture_output=True)
    run_command_sync("sudo snap remove docker-compose", "卸载Snap版本Docker Compose", capture_output=True)

    run_command_sync("sudo apt-get purge -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin", "卸载Docker CE", capture_output=True)
    run_command_sync("sudo apt-get purge -y docker.io docker-doc docker-compose podman-docker containerd runc", "卸载其他Docker包", capture_output=True)

    # 卸载 Docker Compose v1
    run_command_sync("sudo rm -f /usr/local/bin/docker-compose", "卸载 Docker Compose v1", capture_output=True)

    run_command_sync("sudo rm -rf /var/lib/docker", "清理Docker数据目录", capture_output=True)
    run_command_sync("sudo rm -rf /var/lib/containerd", "清理containerd数据目录", capture_output=True)

    run_command_sync("sudo rm -f /etc/docker/daemon.json", "清理Docker配置文件", capture_output=True)
    run_command_sync("sudo rm -f /etc/apt/sources.list.d/docker.list", "清理Docker软件源", capture_output=True)
    run_command_sync("sudo rm -f /etc/apt/keyrings/docker.gpg", "清理Docker GPG密钥", capture_output=True)
    run_command_sync("sudo rm -f /usr/share/keyrings/docker-archive-keyring.gpg", "清理旧版Docker密钥", capture_output=True)

    run_command_sync("sudo apt-get autoremove -y", "清理依赖包", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    import shutil

    docker_installed = False
    compose_v1_installed = False
    compose_v2_installed = False

    # 检查 Docker
    if shutil.which('docker'):
        result = run_command_sync("docker --version", capture_output=True, silent=True)
        if result.returncode == 0:
            docker_installed = True
            print('🟢 Docker: 已安装')
            print(f"   版本: {result.stdout.strip()}")

    # 检查 Docker Compose v1
    if shutil.which('docker-compose'):
        result = run_command_sync("docker-compose --version", capture_output=True, silent=True)
        if result.returncode == 0:
            compose_v1_installed = True
            print('🟢 Docker Compose v1: 已安装')
            print(f"   版本: {result.stdout.strip()}")

    # 检查 Docker Compose v2
    result = run_command_sync("docker compose version", capture_output=True, silent=True)
    if result.returncode == 0:
        compose_v2_installed = True
        print('🟢 Docker Compose v2: 已安装')
        print(f"   版本: {result.stdout.strip()}")

    # 检查 Snap 版本
    result = run_command_sync("snap list docker", capture_output=True, silent=True)
    if result.returncode == 0:
        docker_installed = True
        print('🟢 Docker (Snap): 已安装')

    if docker_installed:
        if compose_v1_installed and compose_v2_installed:
            print('✅ 完整安装: Docker + Compose v1 + Compose v2')
        elif compose_v1_installed or compose_v2_installed:
            print('⚠️  部分安装: 建议同时安装 Compose v1 和 v2')
        return 0
    else:
        print('🔴 状态: 未安装')
        return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
