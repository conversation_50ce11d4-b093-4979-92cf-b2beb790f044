#!/usr/bin/env python3
"""
Mozilla Firefox 浏览器
支持平台: Ubuntu/Debian
架构: amd64
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info

from packages.common.package_downloader import download_package
def get_latest_version():
    try:
        response = requests.get(
            "https://product-details.mozilla.org/1.0/firefox_versions.json",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            return data.get('LATEST_FIREFOX_VERSION', '140.0.2')
    except:
        pass
    return "140.0.2"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    try:
        filename = f"firefox-{version}.tar.xz"
        download_url = f"https://download.mozilla.org/?product=firefox-latest&os=linux64&lang=zh-CN"
        local_path = os.path.join(temp_dir, filename)

        print(f"🔄 开始下载 Firefox {version}...")
        print(f"📥 下载地址: {download_url}")

        response = requests.get(download_url, stream=True, timeout=30)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}% ({downloaded_size // 1024 // 1024}MB/{total_size // 1024 // 1024}MB)", end='', flush=True)

        print(f"\n✅ 下载完成: {local_path}")
        return version, local_path
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    downloaded_file, _ = download_package(
        package_name="firefox",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    username, user_home, uid, gid = get_user_info()
    app_dir = os.path.join(user_home, "Applications")
    os.makedirs(app_dir, exist_ok=True)

    extract_dir = os.path.join(app_dir, "firefox")
    if os.path.exists(extract_dir):
        shutil.rmtree(extract_dir)

    subprocess.run(f"tar -xJf {downloaded_file} -C {app_dir}", shell=True, check=True)

    try:
        os.chown(extract_dir, uid, gid)
        for root, dirs, files in os.walk(extract_dir):
            for d in dirs:
                os.chown(os.path.join(root, d), uid, gid)
            for f in files:
                os.chown(os.path.join(root, f), uid, gid)
    except PermissionError:
        pass

    desktop_dir = os.path.join(user_home, ".local/share/applications")
    os.makedirs(desktop_dir, exist_ok=True)

    desktop_file = os.path.join(desktop_dir, "firefox.desktop")
    desktop_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=Firefox
Name[zh_CN]=火狐浏览器
Comment=Browse the World Wide Web
Comment[zh_CN]=浏览互联网
Exec={extract_dir}/firefox %u
Icon={extract_dir}/browser/chrome/icons/default/default128.png
Terminal=false
Categories=Network;WebBrowser;
StartupWMClass=firefox
MimeType=text/html;text/xml;application/xhtml+xml;application/xml;application/vnd.mozilla.xul+xml;application/rss+xml;application/rdf+xml;image/gif;image/jpeg;image/png;x-scheme-handler/http;x-scheme-handler/https;x-scheme-handler/ftp;x-scheme-handler/chrome;video/webm;application/x-xpinstall;
"""

    with open(desktop_file, 'w') as f:
        f.write(desktop_content)

    os.chmod(desktop_file, 0o644)

    try:
        user_info = pwd.getpwnam(username)
        os.chown(desktop_file, user_info.pw_uid, user_info.pw_gid)
    except:
        pass

    print("✅ Firefox 安装完成")
    return True

def uninstall():
    print("🔄 开始卸载Firefox...")

    print("🔄 停止Firefox进程...")
    subprocess.run("pkill -f '^firefox$' 2>/dev/null || true", shell=True, check=False)
    subprocess.run("pkill -f 'firefox-bin' 2>/dev/null || true", shell=True, check=False)
    subprocess.run("pkill -f '/Applications/firefox/firefox' 2>/dev/null || true", shell=True, check=False)

    print("🔄 检查并卸载Snap版本...")
    subprocess.run("sudo snap remove firefox 2>/dev/null || true", shell=True, check=False)

    print("🔄 检查并卸载Flatpak版本...")
    subprocess.run("flatpak uninstall -y org.mozilla.firefox 2>/dev/null || true", shell=True, check=False)

    print("🔄 检查并卸载APT版本...")
    subprocess.run("sudo apt-get purge -y firefox 2>/dev/null || true", shell=True, check=False)
    subprocess.run("sudo apt-get autoremove -y 2>/dev/null || true", shell=True, check=False)

    _, user_home, _, _ = get_user_info()
    app_dir = os.path.join(user_home, "Applications")
    desktop_file = os.path.join(user_home, ".local/share/applications/firefox.desktop")

    print("🔄 清理手动安装的应用文件...")
    firefox_dir = os.path.join(app_dir, "firefox")
    if os.path.exists(firefox_dir):
        subprocess.run(f"rm -rf '{firefox_dir}'", shell=True, check=False)
        print(f"   已删除: {firefox_dir}")

    print("🔄 清理桌面文件...")
    if os.path.exists(desktop_file):
        subprocess.run(f"rm -f '{desktop_file}'", shell=True, check=False)
        print(f"   已删除: {desktop_file}")

    print("🔄 清理用户配置...")
    config_dirs = [
        os.path.join(user_home, ".mozilla"),
        os.path.join(user_home, ".cache/mozilla"),
    ]
    for config_dir in config_dirs:
        if os.path.exists(config_dir):
            subprocess.run(f"rm -rf '{config_dir}'", shell=True, check=False)
            print(f"   已删除: {config_dir}")

    print("✅ 卸载完成")
    return True

def check_status():
    try:
        result = subprocess.run(["snap", "list", "firefox"], capture_output=True, text=True)
        if result.returncode == 0:
            print('🟢 状态: 已安装')
            return 0
    except:
        pass

    try:
        result = subprocess.run(["flatpak", "list", "org.mozilla.firefox"], capture_output=True, text=True)
        if result.returncode == 0:
            print('🟢 状态: 已安装')
            return 0
    except:
        pass

    try:
        result = subprocess.run(["dpkg", "-l", "firefox"], capture_output=True, text=True)
        if result.returncode == 0 and "ii" in result.stdout:
            print('🟢 状态: 已安装')
            return 0
    except:
        pass

    _, user_home, _, _ = get_user_info()
    firefox_dir = os.path.join(user_home, "Applications", "firefox")
    if os.path.exists(firefox_dir) and os.path.exists(os.path.join(firefox_dir, "firefox")):
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
