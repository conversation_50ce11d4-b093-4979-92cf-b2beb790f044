#!/usr/bin/env python3
"""
Samba服务器
支持平台: Ubuntu/Debian
架构: x86_64
"""

import os
import sys
from datetime import datetime

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import run_command_sync

def get_user_config():
    """获取用户配置，回车使用默认值"""
    print("🔧 Samba服务器配置")
    print("=" * 50)

    share_path = input(f"共享目录路径 [/media/mxhou/12T9]: ").strip() or "/media/mxhou/12T9"
    share_name = input(f"共享名称 [12T9]: ").strip() or "12T9"
    server_ip = input(f"服务器IP [***************]: ").strip() or "***************"
    samba_user = input(f"Samba用户名 [mxhou]: ").strip() or "mxhou"
    samba_pass = input(f"Samba密码 [zyc]: ").strip() or "zyc"

    print("\n📋 挂载权限选择:")
    print("1. 只读挂载 (默认)")
    print("2. 读写挂载")
    choice = input("请选择 [1]: ").strip() or "1"
    read_only = choice != "2"

    print("\n✅ 配置确认:")
    print(f"共享目录: {share_path}")
    print(f"共享名称: {share_name}")
    print(f"服务器IP: {server_ip}")
    print(f"用户名: {samba_user}")
    print(f"密码: {samba_pass}")
    print(f"权限: {'只读' if read_only else '读写'}")
    print("=" * 50)

    return share_path, share_name, server_ip, samba_user, samba_pass, read_only

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    share_path, share_name, server_ip, samba_user, samba_pass, read_only = get_user_config()

    print("🔄 开始安装Samba服务器...")

    # 修复可能的dpkg问题
    run_command_sync("sudo dpkg --configure -a", "修复dpkg配置", capture_output=True)
    run_command_sync("sudo apt --fix-broken install -y", "修复损坏的包", capture_output=True)

    run_command_sync("sudo apt update", "更新软件包列表", capture_output=True)

    # 先尝试完全清理samba相关包
    run_command_sync("sudo apt remove --purge -y samba* winbind", "完全清理Samba包", capture_output=True)
    run_command_sync("sudo apt autoremove -y", "清理依赖", capture_output=True)

    # 重新安装
    result = run_command_sync("sudo apt install -y samba samba-common-bin", "安装Samba软件包", capture_output=True)
    if result.returncode != 0:
        print("❌ Samba软件包安装失败")
        return False

    if not os.path.exists(share_path):
        print(f"❌ 共享目录 {share_path} 不存在")
        return False

    # 先创建配置文件，再设置用户
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if os.path.exists("/etc/samba/smb.conf"):
        run_command_sync(f"sudo cp /etc/samba/smb.conf /etc/samba/smb.conf.backup.{timestamp}", "备份原始配置文件", capture_output=True)
    else:
        print("✅ 无需备份配置文件（文件不存在）")

    permission_text = "只读" if read_only else "读写"
    config_content = f"""[global]
workgroup = WORKGROUP
server string = Samba Server
netbios name = SAMBA-SERVER
security = user
map to guest = bad user
dns proxy = no

log file = /var/log/samba/log.%m
max log size = 50
logging = file

server min protocol = SMB2
client min protocol = SMB2
server role = standalone server

load printers = no
printing = bsd
printcap name = /dev/null
disable spoolss = yes

unix extensions = no
bind interfaces only = no

[{share_name}]
comment = {share_name} {permission_text} Share
path = {share_path}
browseable = yes
read only = {"yes" if read_only else "no"}
valid users = {samba_user}
force user = {samba_user}
create mask = 0775
directory mask = 0775
guest ok = no
"""

    with open('/tmp/smb.conf', 'w') as f:
        f.write(config_content)

    run_command_sync("sudo cp /tmp/smb.conf /etc/samba/smb.conf", "创建Samba配置文件")
    run_command_sync("rm -f /tmp/smb.conf", capture_output=True, silent=True)

    run_command_sync("sudo testparm -s", "测试Samba配置", capture_output=True)

    # 现在配置文件已存在，可以设置用户
    result = run_command_sync(f"id {samba_user}", capture_output=True, silent=True)
    if result.returncode != 0:
        run_command_sync(f"sudo useradd -M -s /bin/false {samba_user}", f"创建系统用户 {samba_user}")
    else:
        print(f"✅ 用户 {samba_user} 已存在")

    # 使用更可靠的密码设置方法
    print(f"🔐 为用户 {samba_user} 设置Samba密码...")

    # 方法1: 使用printf (最可靠)
    result = run_command_sync(f"printf '{samba_pass}\\n{samba_pass}\\n' | sudo smbpasswd -a {samba_user}", "设置Samba密码")

    if result.returncode != 0:
        # 方法2: 使用echo子shell
        print("⚠️ 尝试备用密码设置方法...")
        result = run_command_sync(f"(echo '{samba_pass}'; echo '{samba_pass}') | sudo smbpasswd -a {samba_user}", "备用密码设置方法")

        if result.returncode != 0:
            # 方法3: 使用expect (如果可用)
            print("⚠️ 尝试expect方法...")
            expect_script = f"""#!/usr/bin/expect -f
spawn sudo smbpasswd -a {samba_user}
expect "New SMB password:"
send "{samba_pass}\\r"
expect "Retype new SMB password:"
send "{samba_pass}\\r"
expect eof
"""
            with open('/tmp/samba_expect.exp', 'w') as f:
                f.write(expect_script)

            run_command_sync("chmod +x /tmp/samba_expect.exp", capture_output=True, silent=True)
            result = run_command_sync("/tmp/samba_expect.exp", "使用expect设置密码", capture_output=True)
            run_command_sync("rm -f /tmp/samba_expect.exp", capture_output=True, silent=True)

            if result.returncode != 0:
                print("❌ 所有密码设置方法都失败了")
                return False

    run_command_sync(f"sudo smbpasswd -e {samba_user}", "启用Samba用户", capture_output=True)

    # 验证用户是否成功添加
    verify_result = run_command_sync("sudo pdbedit -L", capture_output=True, silent=True)
    if verify_result.returncode == 0 and samba_user in verify_result.stdout:
        print(f"✅ Samba用户 {samba_user} 验证成功")
    else:
        print(f"❌ Samba用户 {samba_user} 验证失败")
        return False

    run_command_sync("sudo systemctl enable smbd nmbd", "启用Samba服务", capture_output=True)

    # 确保目录存在并设置权限
    run_command_sync(f"sudo mkdir -p /var/log/samba", "创建日志目录", capture_output=True)
    run_command_sync(f"sudo mkdir -p /etc/apparmor.d/samba", "创建AppArmor目录", capture_output=True)
    run_command_sync(f"sudo touch /etc/apparmor.d/samba/smbd-shares", "创建AppArmor配置文件", capture_output=True)
    run_command_sync(f"sudo chown -R root:root /var/log/samba", "设置日志目录权限", capture_output=True)

    run_command_sync("sudo systemctl restart smbd nmbd", "重启Samba服务")

    # 检查服务状态
    import time
    time.sleep(2)
    result = run_command_sync("sudo systemctl is-active smbd", capture_output=True, silent=True)
    if result.returncode != 0:
        print("⚠️ smbd服务启动失败，尝试查看错误信息...")
        run_command_sync("sudo systemctl status smbd --no-pager -l", "检查smbd状态")
        return False

    result = run_command_sync("sudo systemctl is-active nmbd", capture_output=True, silent=True)
    if result.returncode != 0:
        print("⚠️ nmbd服务启动失败，尝试查看错误信息...")
        run_command_sync("sudo systemctl status nmbd --no-pager -l", "检查nmbd状态")
        return False

    print("✅ Samba服务器安装完成")
    print("\n" + "=" * 50)
    print("📋 访问信息:")
    print(f"- 服务器: \\\\{server_ip}\\{share_name}")
    print(f"- 用户名: {samba_user}")
    print(f"- 密码: {samba_pass}")
    print(f"- 权限: {'只读' if read_only else '读写'}")
    print("\n🪟 Windows访问方法:")
    print("1. 清除缓存: net use * /delete /y")
    print(f"2. 连接共享: net use * \\\\{server_ip}\\{share_name} /user:{samba_user} {samba_pass}")
    print(f"3. 或在文件管理器输入: \\\\{server_ip}\\{share_name}")
    print("\n🐧 Ubuntu/Linux访问方法:")
    print("1. 安装客户端: sudo apt install cifs-utils")
    print("2. 创建挂载点: sudo mkdir -p /mnt/samba")
    print(f"3. 挂载共享: sudo mount -t cifs //{server_ip}/{share_name} /mnt/samba -o username={samba_user},password={samba_pass}")
    print("4. 卸载共享: sudo umount /mnt/samba")
    print("5. 图形界面: 文件管理器地址栏输入 smb://{server_ip}/{share_name}")
    print("\n📱 macOS访问方法:")
    print("1. Finder → 前往 → 连接服务器")
    print(f"2. 输入地址: smb://{server_ip}/{share_name}")
    print(f"3. 输入用户名: {samba_user} 和密码: {samba_pass}")
    print("=" * 50)
    return True

def uninstall():
    print("🔄 开始卸载Samba服务器...")

    run_command_sync("sudo systemctl stop smbd nmbd", "停止Samba服务")
    run_command_sync("sudo systemctl disable smbd nmbd", "禁用Samba服务")

    # 尝试从配置文件中获取用户列表，如果失败则使用默认用户
    result = run_command_sync("sudo pdbedit -L", capture_output=True, silent=True)
    if result.returncode == 0 and result.stdout.strip():
        users = [line.split(':')[0] for line in result.stdout.strip().split('\n') if line.strip()]
        for user in users:
            run_command_sync(f"sudo smbpasswd -x {user}", f"删除Samba用户 {user}", capture_output=True)
            # 检查用户是否正在被进程使用
            check_result = run_command_sync(f"sudo lsof -t /home/<USER>/dev/null || true", capture_output=True, silent=True)
            if check_result.stdout.strip():
                print(f"⚠️ 用户 {user} 正在被进程使用，跳过删除系统用户")
            else:
                run_command_sync(f"sudo userdel {user}", f"删除系统用户 {user}", capture_output=True)
    else:
        # 如果无法获取用户列表，尝试删除默认用户
        default_user = "mxhou"
        run_command_sync(f"sudo smbpasswd -x {default_user}", f"删除Samba用户 {default_user}", capture_output=True)
        # 检查用户是否正在被进程使用
        check_result = run_command_sync(f"sudo lsof -t /home/<USER>/dev/null || true", capture_output=True, silent=True)
        if check_result.stdout.strip():
            print(f"⚠️ 用户 {default_user} 正在被进程使用，跳过删除系统用户")
        else:
            run_command_sync(f"sudo userdel {default_user}", f"删除系统用户 {default_user}", capture_output=True)

    run_command_sync("sudo apt remove --purge -y samba samba-common-bin", "卸载Samba软件包", capture_output=True)
    run_command_sync("sudo apt autoremove -y", "清理依赖包", capture_output=True)

    config_files = [
        "/etc/samba/smb.conf",
        "/var/lib/samba",
        "/var/log/samba"
    ]

    for config_file in config_files:
        if os.path.exists(config_file):
            run_command_sync(f"sudo rm -rf '{config_file}'", f"清理配置文件 {config_file}", capture_output=True)

    run_command_sync("sudo rm -f /etc/samba/smb.conf.backup.*", "清理备份配置文件", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    result = run_command_sync("systemctl is-active smbd", capture_output=True, silent=True)
    if result.returncode == 0 and result.stdout.strip() == "active":
        result2 = run_command_sync("systemctl is-active nmbd", capture_output=True, silent=True)
        if result2.returncode == 0 and result2.stdout.strip() == "active":
            print('🟢 状态: 已安装')
            return 0

    result = run_command_sync("dpkg -l | grep samba", capture_output=True, silent=True)
    if result.returncode == 0 and "samba" in result.stdout:
        print('🟡 状态: 部分安装')
        return 1

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
