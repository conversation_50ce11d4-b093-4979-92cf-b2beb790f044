#!/usr/bin/env python3
"""
ZeroTier One
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys
import subprocess
import time
import urllib.request

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from packages.common.package_downloader import download_package

def download(temp_dir, download_now=True):
    if not download_now:
        return "latest", None

    try:
        planet_url = "***************************************/planet"
        local_path = os.path.join(temp_dir, "planet")

        urllib.request.urlretrieve(planet_url, local_path)

        return "latest", local_path
    except Exception as e:
        print(f"❌ 下载 planet 文件失败: {e}")
        return "latest", None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    print("🚀 开始安装 ZeroTier One...")

    if os.geteuid() != 0:
        print("❌ 请以 root 权限运行此脚本")
        return False

    print("📦 更新软件包列表...")
    subprocess.run(["apt-get", "update"], check=False)

    if not os.path.exists("/usr/bin/curl"):
        print("📦 安装 curl...")
        subprocess.run(["apt-get", "install", "-y", "curl"], check=False)

    print("📥 下载并安装 ZeroTier...")
    result = subprocess.run(
        "curl -s https://install.zerotier.com | bash",
        shell=True, check=False
    )

    if result.returncode != 0:
        print("❌ ZeroTier 安装失败")
        return False

    time.sleep(2)

    if not os.path.exists("/usr/sbin/zerotier-cli"):
        print("❌ ZeroTier 安装失败")
        return False

    network_ids = ["e4e941a4569e1988", "e4e941a4565cde2f", "e4e941a456657761"]
    result = subprocess.run(
        ["zerotier-cli", "listnetworks"],
        capture_output=True, text=True, check=False
    )

    for network_id in network_ids:
        if network_id not in result.stdout:
            print(f"🌐 加入 ZeroTier 网络 {network_id}...")
            subprocess.run(
                ["zerotier-cli", "join", network_id],
                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, check=False
            )

    print("⚙️ 启用 ZeroTier 服务...")
    subprocess.run(
        ["systemctl", "enable", "zerotier-one.service"],
        stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, check=False
    )

    downloaded_file, _ = download_package(
        package_name="zerotier-planet",
        online_downloader=download,
        package_type="generic"
    )

    if downloaded_file and os.path.exists(downloaded_file):
        print("⚙️ 配置 planet 文件...")
        os.makedirs("/var/lib/zerotier-one", exist_ok=True)
        subprocess.run(
            ["cp", downloaded_file, "/var/lib/zerotier-one/planet"],
            check=False
        )

    print("🔄 重启 ZeroTier 服务...")
    subprocess.run(
        ["systemctl", "restart", "zerotier-one"],
        stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, check=False
    )

    time.sleep(5)

    result = subprocess.run(
        ["zerotier-cli", "peers"],
        capture_output=True, text=True, check=False
    )

    if "200" in result.stdout:
        print("✅ ZeroTier One 安装完成！")
    else:
        print("⚠️ ZeroTier 安装完成，但连接测试未通过")

    return True

def uninstall():
    print("🗑️ 开始卸载 ZeroTier...")

    print("🔄 停止 ZeroTier 服务...")
    subprocess.run(
        ["systemctl", "stop", "zerotier-one"],
        stderr=subprocess.DEVNULL, check=False
    )
    subprocess.run(
        ["systemctl", "disable", "zerotier-one"],
        stderr=subprocess.DEVNULL, check=False
    )

    print("🔄 检查并卸载Snap版本...")
    subprocess.run(
        "snap remove zerotier-one 2>/dev/null || true",
        shell=True, check=False
    )

    print("🔄 卸载软件包...")
    subprocess.run(["apt-get", "purge", "-y", "zerotier-one"], check=False)
    subprocess.run(["apt-get", "autoremove", "-y"], check=False)

    print("🔄 清理配置文件...")
    subprocess.run(["rm", "-rf", "/var/lib/zerotier-one"], check=False)

    print("✅ ZeroTier 卸载完成！")
    return True

def check_status():
    if os.path.exists("/usr/sbin/zerotier-cli"):
        result = subprocess.run(
            ["systemctl", "is-active", "zerotier-one"],
            capture_output=True, text=True, check=False
        )
        if result.returncode == 0 and result.stdout.strip() == "active":
            print("🟢 状态: 已安装")
            return 0
        else:
            print("🟡 状态: 已安装")
            return 0
    else:
        print("🔴 状态: 未安装")
        return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
