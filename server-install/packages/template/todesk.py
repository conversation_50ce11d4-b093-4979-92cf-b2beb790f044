#!/usr/bin/env python3
"""
ToDesk 远程桌面
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from packages.common.package_downloader import download_package
def get_latest_version():
    try:
        response = requests.get(
            "https://www.todesk.com/linux.html",
            timeout=10
        )
        if response.status_code == 200:
            import re
            version_match = re.search(r'todesk-v(\d+\.\d+\.\d+\.\d+)-amd64\.deb', response.text)
            if version_match:
                return version_match.group(1)
    except:
        pass
    return "4.7.2.0"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    filename = f"todesk-v{version}-amd64.deb"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载ToDesk...")
        # 使用您提供的可用下载链接
        download_url = f"https://newdl.todesk.com/linux/todesk-v{version}-amd64.deb"

        # 使用wget下载，这是您代码中验证可用的方法
        result = subprocess.run([
            "wget", download_url, "-O", local_path
        ], capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
                print(f"✅ 下载成功")
                return version, local_path
            else:
                print(f"❌ 下载的文件大小异常")
                if os.path.exists(local_path):
                    os.remove(local_path)
        else:
            print(f"❌ wget下载失败: {result.stderr}")

        return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        # 先卸载已安装的版本
        uninstall_result = uninstall()
        if not uninstall_result:
            print("❌ 卸载失败，无法继续安装")
            return False

    downloaded_file, _ = download_package(
        package_name="todesk",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    # 不再需要用户信息

    try:
        print("🔄 安装ToDesk...")

        # 设置环境变量，使用非交互式模式
        env = os.environ.copy()
        env["DEBIAN_FRONTEND"] = "noninteractive"

        # 首先尝试安装依赖
        print("🔄 更新包列表...")
        subprocess.run(["sudo", "apt", "update"], check=False, env=env)

        print("🔄 安装依赖...")
        subprocess.run(["sudo", "DEBIAN_FRONTEND=noninteractive", "apt", "install", "-y", "-f"],
                      check=False, env=env)

        # 使用dpkg安装deb包
        print(f"🔄 安装deb包: {downloaded_file}")
        result = subprocess.run(["sudo", "dpkg", "-i", downloaded_file],
                              capture_output=True, text=True, env=env)

        if result.returncode != 0:
            print(f"⚠️ dpkg安装可能有依赖问题，尝试修复...")
            print(f"dpkg输出: {result.stderr}")

            # 修复依赖
            subprocess.run(["sudo", "DEBIAN_FRONTEND=noninteractive", "apt", "install", "-f", "-y"],
                          check=True, env=env)

            # 再次尝试安装
            result2 = subprocess.run(["sudo", "dpkg", "-i", downloaded_file],
                                   capture_output=True, text=True, env=env)
            if result2.returncode != 0:
                print(f"❌ 安装ToDesk失败: {result2.stderr}")
                return False

        print("✅ ToDesk包安装完成")

        # 验证安装
        verify_result = subprocess.run(["dpkg", "-l", "todesk"], capture_output=True, text=True)
        if verify_result.returncode == 0 and "ii" in verify_result.stdout:
            print("✅ ToDesk安装验证成功")
        else:
            print("❌ ToDesk安装验证失败")
            return False

    except Exception as e:
        print(f"❌ 安装ToDesk时出错: {e}")
        return False

    # 验证安装并配置服务
    print("🔄 检查安装的文件...")
    executable_paths = ["/usr/bin/todesk", "/usr/local/bin/todesk", "/opt/todesk/todesk"]

    for path in executable_paths:
        if os.path.exists(path):
            print(f"✅ 找到ToDesk可执行文件: {path}")
            break
    else:
        print("❌ 未找到ToDesk可执行文件")
        return False

    # 配置并启动服务
    print("🔄 配置ToDesk服务...")
    services = ["todeskd", "todesk"]  # 优先尝试todeskd

    for service in services:
        try:
            result = subprocess.run(["systemctl", "list-unit-files", f"{service}.service"],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"🔍 找到ToDesk服务: {service}.service")
                subprocess.run(["sudo", "systemctl", "daemon-reload"], check=False, timeout=10)
                subprocess.run(["sudo", "systemctl", "start", service], check=True, timeout=30)
                subprocess.run(["sudo", "systemctl", "enable", service], check=True, timeout=10)
                print(f"✅ ToDesk服务 {service}.service 已启动并设置为开机自启")
                break
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError) as e:
            print(f"⚠️ 配置服务 {service} 失败: {e}")
            continue
    else:
        print("⚠️ 未找到可用的ToDesk服务，但安装可能成功")

    # 安装完成，服务已配置

    print("✅ ToDesk安装完成")
    return True

def uninstall():
    """卸载ToDesk - 优化版本"""
    print("🔄 开始卸载ToDesk...")

    try:
        # 1. 停止服务
        print("🔄 停止ToDesk服务...")
        services = ["todesk", "todeskd"]
        for service in services:
            try:
                subprocess.run(["sudo", "systemctl", "stop", service],
                             check=False, timeout=15,
                             stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                subprocess.run(["sudo", "systemctl", "disable", service],
                             check=False, timeout=15,
                             stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            except subprocess.TimeoutExpired:
                continue  # 忽略超时，继续下一个

        # 2. 卸载Snap版本（如果存在）
        print("🔄 检查并卸载Snap版本...")
        try:
            subprocess.run("sudo snap remove todesk 2>/dev/null || true",
                         shell=True, check=False, timeout=30,
                         stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.TimeoutExpired:
            pass  # 忽略超时

        # 3. 卸载deb包
        print("🔄 卸载deb包...")
        env = os.environ.copy()
        env["DEBIAN_FRONTEND"] = "noninteractive"

        try:
            subprocess.run(["sudo", "DEBIAN_FRONTEND=noninteractive", "apt-get",
                          "remove", "--purge", "-y", "todesk"],
                          check=False, env=env, timeout=180)
            subprocess.run(["sudo", "DEBIAN_FRONTEND=noninteractive", "apt-get",
                          "autoremove", "-y"],
                          check=False, env=env, timeout=60)
        except subprocess.TimeoutExpired:
            print("⚠️ apt-get操作超时，但可能已完成")

        # 4. 清理系统文件
        print("🔄 清理系统文件...")
        cleanup_paths = [
            "/usr/bin/todesk",
            "/usr/local/bin/todesk",
            "/usr/share/applications/todesk.desktop",
            "/opt/todesk"
        ]

        for path in cleanup_paths:
            try:
                subprocess.run(f"sudo rm -rf '{path}' 2>/dev/null || true",
                             shell=True, check=False, timeout=5,
                             stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            except subprocess.TimeoutExpired:
                continue  # 忽略超时，继续下一个

        # 5. 清理用户配置
        print("🔄 清理用户配置文件...")
        try:
            # 清理所有用户的ToDesk配置
            subprocess.run("sudo find /home -name '.config' -type d -exec find {} -name 'todesk' -type d -exec rm -rf {} + 2>/dev/null || true",
                         shell=True, check=False, timeout=15,
                         stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except subprocess.TimeoutExpired:
            pass  # 忽略超时

        print("✅ 卸载完成")
        return True

    except Exception as e:
        print(f"❌ 卸载过程中出现错误: {e}")
        return False

def check_status():
    if shutil.which('todesk'):
        print('🟢 状态: 已安装')
        return 0

    result = subprocess.run(["snap", "list", "todesk"], capture_output=True, text=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)