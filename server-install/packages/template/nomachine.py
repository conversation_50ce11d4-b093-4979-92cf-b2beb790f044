
#!/usr/bin/env python3
"""
NoMachine
支持平台: Ubuntu/Debian
架构: x86_64
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

from packages.common.package_downloader import download_package
def get_latest_version():
    return "8.16.1"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    filename = f"nomachine_{version}_1_amd64.deb"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载NoMachine...")
        download_url = f"https://download.nomachine.com/download/8.16/Linux/{filename}"

        response = requests.get(download_url, stream=True, timeout=60)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

        print()

        if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    downloaded_file, _ = download_package(
        package_name="nomachine",
        online_downloader=download,
        package_type="deb"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    run_command_sync(f"sudo dpkg -i '{downloaded_file}'", "安装NoMachine", capture_output=True)
    run_command_sync("sudo apt install -f -y", "修复依赖", capture_output=True)

    run_command_sync("sudo /usr/NX/bin/nxserver --start", "启动NoMachine服务", capture_output=True)

    print("✅ NoMachine安装完成")
    return True

def uninstall():
    print("🔄 开始卸载NoMachine...")

    current_pid = os.getpid()
    current_ppid = os.getppid()
    protected_pids = {current_pid, current_ppid}

    python_result = run_command_sync("pgrep -f 'python.*nomachine'", capture_output=True, silent=True)
    if python_result.returncode == 0:
        for pid in python_result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    protected_pids.add(int(pid.strip()))
                except ValueError:
                    continue

    result = run_command_sync("pgrep -f 'nxserver\\|nxplayer\\|nxclient'", "查找NoMachine进程", capture_output=True, silent=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    pid_num = int(pid.strip())
                    if pid_num not in protected_pids:
                        run_command_sync(f"kill {pid_num}", f"停止进程 {pid_num}", capture_output=True)
                except ValueError:
                    continue

    run_command_sync("sudo /usr/NX/bin/nxserver --stop", "停止NoMachine服务", capture_output=True)
    run_command_sync("sudo snap remove nomachine", "检查并卸载Snap版本", capture_output=True)
    run_command_sync("sudo dpkg -r nomachine", "卸载NoMachine包", capture_output=True)
    run_command_sync("sudo apt autoremove -y", "清理残余依赖", capture_output=True)

    _, user_home, _, _ = get_user_info()
    config_dirs = [
        "/usr/NX",
        "/var/lib/nxserver",
        "/var/log/nx",
        os.path.join(user_home, ".nx"),
        os.path.join(user_home, ".config/NoMachine")
    ]

    for config_dir in config_dirs:
        if os.path.exists(config_dir):
            run_command_sync(f"sudo rm -rf '{config_dir}'", f"清理配置目录 {os.path.basename(config_dir)}", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    if os.path.exists("/usr/NX/bin/nxplayer") or os.path.exists("/usr/NX/bin/nxserver"):
        print('🟢 状态: 已安装')
        return 0

    result = run_command_sync("snap list nomachine", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
