#!/usr/bin/env python3
"""
Bash配置模块
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    _, user_home, uid, gid = get_user_info()

    print("🚀 开始配置Bash...")

    run_command_sync(f"cp {user_home}/.bashrc {user_home}/.bashrc.backup", "备份原始bashrc文件", capture_output=True)

    bash_config = f"""

# Ubuntu Install Script Aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
alias ..='cd ..'
alias ...='cd ../..'
alias update='sudo apt update && sudo apt upgrade -y'
alias install='sudo apt install'
alias remove='sudo apt remove'
alias search='apt search'
alias cls='clear'
alias grep='grep --color=auto'
alias fgrep='fgrep --color=auto'
alias egrep='egrep --color=auto'

"""

    with open(f"{user_home}/.bashrc", 'a') as f:
        f.write(bash_config)

    inputrc_config = """# 历史记录搜索 - 使用上下箭头键
"\\e[A": history-search-backward
"\\e[B": history-search-forward

# 大小写不敏感的自动补全
set completion-ignore-case on

# 显示所有匹配项
set show-all-if-ambiguous on

"""

    with open(f"{user_home}/.inputrc", 'w') as f:
        f.write(inputrc_config)

    try:
        os.chown(f"{user_home}/.bashrc", uid, gid)
        os.chown(f"{user_home}/.inputrc", uid, gid)
    except PermissionError:
        pass

    print("✅ Bash配置完成")
    return True

def uninstall():
    print("🔄 开始卸载Bash配置...")

    _, user_home, uid, gid = get_user_info()

    run_command_sync(f"sed -i '/# Ubuntu Install Script Aliases/,/^$/d' {user_home}/.bashrc", "清理bashrc配置", capture_output=True)

    run_command_sync(f"rm -f {user_home}/.inputrc", "清理inputrc文件", capture_output=True)

    if os.path.exists(f"{user_home}/.bashrc.backup"):
        run_command_sync(f"cp {user_home}/.bashrc.backup {user_home}/.bashrc", "恢复原始bashrc文件", capture_output=True)

    try:
        os.chown(f"{user_home}/.bashrc", uid, gid)
    except PermissionError:
        pass

    print("✅ 卸载完成")
    return True

def check_status():
    _, user_home, _, _ = get_user_info()

    aliases_configured = False
    bashrc_path = f"{user_home}/.bashrc"
    if os.path.exists(bashrc_path):
        try:
            with open(bashrc_path, 'r', encoding='utf-8') as f:
                content = f.read()
                aliases_configured = "# Ubuntu Install Script Aliases" in content
        except:
            pass

    inputrc_configured = False
    inputrc_path = f"{user_home}/.inputrc"
    if os.path.exists(inputrc_path):
        try:
            with open(inputrc_path, 'r', encoding='utf-8') as f:
                content = f.read()
                inputrc_configured = "history-search-backward" in content
        except:
            pass

    if aliases_configured and inputrc_configured:
        print('🟢 状态: 已安装')
        return 0
    elif aliases_configured or inputrc_configured:
        print('🟡 状态: 已安装')
        return 0
    else:
        print('🔴 状态: 未安装')
        return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
