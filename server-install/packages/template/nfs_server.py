#!/usr/bin/env python3
"""
NFS服务器
支持平台: Ubuntu/Debian
架构: x86_64
"""

import os
import sys
from datetime import datetime

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import run_command_sync, get_user_info

def get_user_config():
    """获取用户配置，回车使用默认值"""
    print("🔧 NFS服务器配置")
    print("=" * 50)

    share_path = input(f"共享目录路径 [/media/mxhou/12T9]: ").strip() or "/media/mxhou/12T9"
    server_ip = input(f"服务器IP [***************]: ").strip() or "***************"
    client_subnet = input(f"客户端网段 [*************/24]: ").strip() or "*************/24"

    print("\n📋 权限选择:")
    print("1. 只读权限 (默认)")
    print("2. 读写权限")
    choice = input("请选择 [1]: ").strip() or "1"
    read_only = choice != "2"

    print("\n✅ 配置确认:")
    print(f"共享目录: {share_path}")
    print(f"服务器IP: {server_ip}")
    print(f"客户端网段: {client_subnet}")
    print(f"权限: {'只读' if read_only else '读写'}")
    print("=" * 50)

    return share_path, server_ip, client_subnet, read_only

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    share_path, server_ip, client_subnet, read_only = get_user_config()

    print("🔄 开始安装NFS服务器...")

    run_command_sync("sudo apt update", "更新软件包列表", capture_output=True)

    result = run_command_sync("sudo apt install -y nfs-kernel-server", "安装NFS内核服务器", capture_output=True)
    if result.returncode != 0:
        print("❌ NFS服务器安装失败")
        return False

    if not os.path.exists(share_path):
        print(f"❌ 共享目录 {share_path} 不存在")
        return False

    username, _, _, _ = get_user_info()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if os.path.exists("/etc/exports"):
        run_command_sync(f"sudo cp /etc/exports /etc/exports.backup.{timestamp}", "备份exports文件", capture_output=True)

    permission = "ro" if read_only else "rw"
    export_line = f"{share_path} {client_subnet}({permission},sync,no_subtree_check,no_root_squash)"

    with open('/tmp/exports', 'w') as f:
        f.write(f"# NFS exports configuration\n")
        f.write(f"# Created on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"{export_line}\n")

    run_command_sync("sudo cp /tmp/exports /etc/exports", "创建NFS exports配置")
    run_command_sync("rm -f /tmp/exports", capture_output=True, silent=True)

    run_command_sync(f"sudo chown -R {username}:{username} {share_path}", f"设置目录所有者", capture_output=True)
    run_command_sync(f"sudo chmod 755 {share_path}", "设置目录权限", capture_output=True)

    run_command_sync("sudo exportfs -a", "导出NFS共享")
    run_command_sync("sudo systemctl enable nfs-kernel-server", "启用NFS服务", capture_output=True)
    run_command_sync("sudo systemctl restart nfs-kernel-server", "重启NFS服务")

    import time
    time.sleep(2)
    result = run_command_sync("sudo systemctl is-active nfs-kernel-server", capture_output=True, silent=True)
    if result.returncode != 0:
        print("⚠️ NFS服务启动失败，尝试查看错误信息...")
        run_command_sync("sudo systemctl status nfs-kernel-server --no-pager -l", "检查NFS状态")
        return False

    print("✅ NFS服务器安装完成")
    print("\n" + "=" * 50)
    print("📋 访问信息:")
    print(f"- 服务器: {server_ip}")
    print(f"- 共享路径: {share_path}")
    print(f"- 客户端网段: {client_subnet}")
    print(f"- 权限: {'只读' if read_only else '读写'}")
    print("\n🐧 Linux客户端挂载方法:")
    print("1. 安装客户端: sudo apt install nfs-common")
    print("2. 创建挂载点: sudo mkdir -p /mnt/nfs")
    print(f"3. 挂载共享: sudo mount -t nfs {server_ip}:{share_path} /mnt/nfs")
    print("4. 卸载共享: sudo umount /mnt/nfs")
    print("\n📱 永久挂载 (添加到/etc/fstab):")
    print(f"{server_ip}:{share_path} /mnt/nfs nfs defaults,_netdev 0 0")
    print("=" * 50)
    return True

def uninstall():
    print("🔄 开始卸载NFS服务器...")

    run_command_sync("sudo systemctl stop nfs-kernel-server", "停止NFS服务", capture_output=True)
    run_command_sync("sudo systemctl disable nfs-kernel-server", "禁用NFS服务", capture_output=True)

    run_command_sync("sudo exportfs -ua", "取消所有NFS导出", capture_output=True)

    run_command_sync("sudo apt remove --purge -y nfs-kernel-server nfs-common rpcbind", "卸载NFS相关软件包", capture_output=True)
    run_command_sync("sudo apt autoremove -y", "清理依赖包", capture_output=True)

    result = run_command_sync("snap list | grep nfs", capture_output=True, silent=True)
    if result.returncode == 0 and result.stdout.strip():
        run_command_sync("sudo snap remove nfs-kernel-server", "卸载snap版本NFS", capture_output=True)

    config_files = [
        "/etc/exports",
        "/var/lib/nfs",
        "/etc/default/nfs-kernel-server",
        "/etc/default/nfs-common"
    ]

    for config_file in config_files:
        if os.path.exists(config_file):
            run_command_sync(f"sudo rm -rf '{config_file}'", f"清理配置文件 {config_file}", capture_output=True)

    run_command_sync("sudo rm -f /etc/exports.backup.*", "清理备份配置文件", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    result = run_command_sync("systemctl is-active nfs-kernel-server", capture_output=True, silent=True)
    if result.returncode == 0 and result.stdout.strip() == "active":
        print('🟢 状态: 已安装')
        return 0

    result = run_command_sync("dpkg -l | grep nfs-kernel-server", capture_output=True, silent=True)
    if result.returncode == 0 and "nfs-kernel-server" in result.stdout:
        print('🟡 状态: 部分安装')
        return 1

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
