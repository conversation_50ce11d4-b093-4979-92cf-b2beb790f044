#!/usr/bin/env python3
"""
思源笔记
支持平台: Ubuntu/Debian
架构: x86_64
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

from packages.common.package_downloader import download_package

def get_latest_version():
    try:
        response = requests.get(
            "https://api.github.com/repos/siyuan-note/siyuan/releases/latest",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            return data.get('tag_name', '').lstrip('v')
    except:
        pass
    return "3.2.0"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    filename = f"siyuan-{version}-linux.AppImage"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载思源笔记...")
        download_url = f"https://github.com/siyuan-note/siyuan/releases/download/v{version}/{filename}"

        response = requests.get(download_url, stream=True, timeout=60)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

        print()

        if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    downloaded_file, _ = download_package(
        package_name="siyuan",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    _, user_home, uid, gid = get_user_info()
    app_dir = os.path.join(user_home, "Applications")
    os.makedirs(app_dir, exist_ok=True)

    filename = os.path.basename(downloaded_file)
    target_path = os.path.join(app_dir, filename)

    shutil.copy2(downloaded_file, target_path)
    os.chmod(target_path, 0o755)

    try:
        os.chown(target_path, uid, gid)
    except PermissionError:
        pass

    # 设置图标文件
    icon_dir = os.path.join(user_home, ".local/share/icons")
    os.makedirs(icon_dir, exist_ok=True)

    # 复制PNG图标文件 - 使用相对路径
    # 从当前文件 packages/template/siyuan.py 到 packages/common/resources/siyuan-icon.png
    current_file_dir = os.path.dirname(os.path.abspath(__file__))
    icon_path = os.path.join(current_file_dir, "..", "common", "resources", "siyuan-icon.png")
    source_icon = os.path.abspath(icon_path)  # 转换为绝对路径

    target_icon = os.path.join(icon_dir, "siyuan.png")

    if os.path.exists(source_icon):
        shutil.copy2(source_icon, target_icon)
        os.chmod(target_icon, 0o644)
        try:
            os.chown(target_icon, uid, gid)
            os.chown(icon_dir, uid, gid)
        except PermissionError:
            pass
        print(f"✅ 图标文件已复制到: {target_icon}")
        icon_path = target_icon
    else:
        print(f"⚠️  图标文件不存在: {source_icon}")
        print(f"   使用默认图标")
        icon_path = "siyuan"

    desktop_dir = os.path.join(user_home, ".local/share/applications")
    os.makedirs(desktop_dir, exist_ok=True)

    desktop_file = os.path.join(desktop_dir, "siyuan.desktop")
    desktop_content = f"""[Desktop Entry]
Version=1.0
Type=Application
Name=思源笔记
Name[en]=SiYuan
Comment=重构你的思维
Comment[en]=Refactor your thinking
Exec={target_path} --no-sandbox %U
Icon={icon_path}
Terminal=false
Categories=Office;TextEditor;
StartupWMClass=SiYuan
MimeType=application/x-siyuan;
"""

    with open(desktop_file, 'w') as f:
        f.write(desktop_content)

    os.chmod(desktop_file, 0o644)

    try:
        os.chown(desktop_file, uid, gid)
        os.chown(desktop_dir, uid, gid)
        os.chown(app_dir, uid, gid)
    except PermissionError:
        pass

    # 刷新图标缓存
    try:
        subprocess.run(["gtk-update-icon-cache", "-f", "-t", icon_dir],
                      check=False, capture_output=True)
        print("✅ 图标缓存已刷新")
    except Exception:
        pass

    print("✅ 思源笔记安装完成")
    return True

def uninstall():
    print("🔄 开始卸载思源笔记...")

    current_pid = os.getpid()
    current_ppid = os.getppid()

    # 获取所有可能的安装相关进程PID，避免误杀
    protected_pids = {current_pid, current_ppid}

    # 查找Python安装进程
    python_result = run_command_sync("pgrep -f 'python.*siyuan'", capture_output=True, silent=True)
    if python_result.returncode == 0:
        for pid in python_result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    protected_pids.add(int(pid.strip()))
                except ValueError:
                    continue

    result = run_command_sync("pgrep -f 'siyuan.*AppImage'", "查找思源笔记进程", capture_output=True, silent=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    pid_num = int(pid.strip())
                    if pid_num not in protected_pids:
                        run_command_sync(f"kill {pid_num}", f"停止进程 {pid_num}", capture_output=True)
                except ValueError:
                    continue

    run_command_sync("sudo snap remove siyuan", "检查并卸载Snap版本", capture_output=True)

    _, user_home, _, _ = get_user_info()
    app_dir = os.path.join(user_home, "Applications")
    desktop_file = os.path.join(user_home, ".local/share/applications/siyuan.desktop")

    if os.path.exists(app_dir):
        for file in os.listdir(app_dir):
            if file.startswith("siyuan") and file.endswith(".AppImage"):
                file_path = os.path.join(app_dir, file)
                run_command_sync(f"rm -f '{file_path}'", f"清理应用文件 {file}", capture_output=True)

    run_command_sync(f"rm -f '{desktop_file}'", "清理桌面文件", capture_output=True)

    # 清理图标文件
    icon_file = os.path.join(user_home, ".local/share/icons/siyuan.png")
    if os.path.exists(icon_file):
        run_command_sync(f"rm -f '{icon_file}'", "清理图标文件", capture_output=True)

        # 刷新图标缓存
        icon_dir = os.path.join(user_home, ".local/share/icons")
        try:
            subprocess.run(["gtk-update-icon-cache", "-f", "-t", icon_dir],
                          check=False, capture_output=True)
            print("✅ 图标缓存已刷新")
        except Exception:
            pass

    config_dirs = [
        os.path.join(user_home, ".config/siyuan"),
        os.path.join(user_home, ".cache/siyuan"),
        os.path.join(user_home, ".local/share/siyuan")
    ]

    for config_dir in config_dirs:
        if os.path.exists(config_dir):
            run_command_sync(f"rm -rf '{config_dir}'", f"清理配置目录 {os.path.basename(config_dir)}", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    _, user_home, _, _ = get_user_info()
    app_dir = os.path.join(user_home, "Applications")

    if os.path.exists(app_dir):
        installed_files = [f for f in os.listdir(app_dir) if f.startswith("siyuan") and f.endswith(".AppImage")]
        if installed_files:
            print('🟢 状态: 已安装')
            return 0

    result = run_command_sync("snap list siyuan", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
