#!/usr/bin/env python3
"""
Flameshot 截图工具
支持平台: Ubuntu/Debian
架构: 通用
"""

def install():
    """安装Flameshot截图工具"""
    # 先检查是否已安装
    status_code = check_status()
    if status_code == 0:
        print("✅ Flameshot 截图工具已安装，跳过安装步骤")
        return []

    commands = [
        "sudo apt-get update",
        "sudo apt-get install -y flameshot",
        "echo '✅ Flameshot 截图工具安装完成！'"
    ]
    return commands

def uninstall():
    """卸载Flameshot截图工具"""
    commands = [
        "# 卸载snap版本的Flameshot",
        "sudo snap remove flameshot 2>/dev/null || true",
        "# 卸载apt版本的Flameshot",
        "sudo apt-get purge -y flameshot 2>/dev/null || true",
        "# 卸载flatpak版本的Flameshot",
        "flatpak uninstall -y org.flameshot.Flameshot 2>/dev/null || true",
        "sudo apt-get autoremove -y",
        "echo '✅ Flameshot 截图工具卸载完成！'"
    ]
    return commands

def check_status():
    import shutil

    if shutil.which('flameshot'):
        print("🟢 状态: 已安装")
        return 0
    else:
        print("🔴 状态: 未安装")
        return 2

if __name__ == "__main__":
    import sys
    import subprocess

    def run_commands(commands):
        """执行命令列表"""
        for cmd in commands:
            print(f"执行: {cmd}")
            try:
                result = subprocess.run(cmd, shell=True, check=True,
                                      capture_output=True, text=True)
                if result.stdout:
                    print(result.stdout)
            except subprocess.CalledProcessError as e:
                print(f"❌ 错误: {e}")
                if e.stderr:
                    print(e.stderr)
                return False
        return True

    # 主逻辑
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall":
        print("🗑️ 开始卸载Flameshot...")
        run_commands(uninstall())
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        print("🚀 开始安装Flameshot...")
        run_commands(install())
