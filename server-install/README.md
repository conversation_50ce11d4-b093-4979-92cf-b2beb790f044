# 服务器安装管理系统

双远程仓库
git remote set-url --add --push origin ************************************************/sanyitec/server-install.git
git remote set-url --add --push origin https://<EMAIL>/wadeagle/server-install.git


一个基于Flask的远程服务器软件包安装管理系统，支持通过Web界面管理多台服务器的软件安装、卸载和状态监控。

## 🏗️ 项目结构

```
server-install/
├── app.py                      # Flask Web应用主程序
├── main_installer.py           # 主安装程序（在远程服务器执行）
├── package_downloader.py       # 包下载管理器
├── gitea_package_manager.py    # Gitea包管理器
├── packages/                   # 软件包安装脚本目录
│   ├── template/              # 模板包（标准格式）
│   │   ├── siyuan.py         # 思源笔记安装脚本（标准模板）
│   │   ├── chrome.py         # Chrome浏览器安装脚本
│   │   ├── docker.py         # Docker安装脚本
│   │   └── ...               # 其他软件包脚本
│   └── fridge/               # 冷藏包（待整理）
├── static/                    # Web前端文件
│   ├── index.html            # 主页面
│   ├── terminal.html         # 终端页面
│   ├── script.js             # 前端脚本
│   └── style.css             # 样式文件
├── configs/                   # 配置文件目录
├── logs/                      # 日志文件目录
├── resources/                 # 资源文件（图标等）
└── deploy/                    # Docker部署配置
    ├── Dockerfile
    ├── docker-compose.yml
    └── README.md
```

## 🚀 运行流程

### 1. Web界面操作流程
1. **启动Web服务**: 运行 `python3 app.py` 启动Flask服务
2. **访问界面**: 浏览器访问 `http://localhost:5000`
3. **配置服务器**: 输入目标服务器的SSH连接信息
4. **测试连接**: 验证SSH连接和权限
5. **选择软件包**: 从可用软件包列表中选择要安装的软件
6. **上传文件**: 系统自动上传安装脚本到远程服务器
7. **执行安装**: 远程执行安装队列

### 2. 远程服务器执行流程
1. **环境准备**: 自动配置sudo无密码权限
2. **源更换**: 检测并自动更换为国内软件源
3. **队列处理**: 按顺序执行安装队列中的软件包
4. **状态报告**: 生成安装报告并返回结果

### 3. 软件包管理流程
1. **下载管理**: 优先从Gitea下载，失败则在线下载
2. **版本检查**: 自动获取最新版本信息
3. **智能缓存**: 下载后自动上传到Gitea缓存
4. **状态监控**: 实时检查软件安装状态

## 📦 软件包开发规范

基于 `packages/template/siyuan.py` 模板，所有软件包脚本应遵循以下规范：

### 必需函数结构
```python
def get_user_info():           # 获取用户信息
def run_command_sync():        # 同步执行命令
def get_latest_version():      # 获取最新版本
def download():                # 下载函数
def install():                 # 安装函数
def uninstall():               # 卸载函数
def check_status():            # 状态检查函数
```

### 开发要点
- **函数名称**: 严格按照模板函数名，不可随意增加函数
- **状态输出**: 使用标准状态符号 🟢(运行中) 🟡(已安装未运行) 🔴(未安装)
- **命令执行**: 所有subprocess.run替换为run_command_sync
- **权限处理**: 使用get_user_info()处理用户权限
- **进度显示**: 对有进度条的命令使用capture_output=True
- **下载方式**: 使用package_downloader的download_package函数
- **安装逻辑**: 检查状态→卸载旧版→下载→安装→配置
- **卸载逻辑**: 停止进程→卸载软件→清理配置→清理残余文件
- **Snap支持**: 卸载时考虑snap安装的软件

### 代码风格
- 保持代码简洁，避免非必要注释
- 状态检查函数只输出三种状态，无多余打印
- 手动测试下载源后再添加到代码
- 一个下载源即可，不强行添加多个源

## 🛠️ 部署方式

### 方式1: 直接运行
```bash
cd server-install
python3 app.py
```

### 方式2: Docker部署
```bash
cd server-install/deploy
docker-compose up -d
```

## 🔧 配置说明

### SSH连接配置
- 支持密码认证
- 自动配置sudo无密码权限
- 智能SFTP连接修复

### 软件源配置
- 自动检测当前软件源
- 支持自动切换国内源
- 提升下载速度

### Gitea缓存配置
- 可选的包缓存服务
- 减少重复下载
- 支持版本管理

## 📋 功能特性

- ✅ Web界面管理多台服务器
- ✅ 智能文件同步（只传输变更文件）
- ✅ 实时安装进度显示
- ✅ 软件状态监控
- ✅ 批量安装/卸载
- ✅ 安装报告生成
- ✅ 配置文件管理
- ✅ 终端实时输出
- ✅ Docker容器化部署

## 🎯 使用场景

- 批量服务器软件部署
- 开发环境快速搭建
- 软件包版本管理
- 远程运维自动化

## 📝 开发指南

1. 参考 `siyuan.py` 模板创建新的软件包脚本
2. 遵循函数命名和结构规范
3. 测试安装、卸载、状态检查功能
4. 确保支持用户权限和清理功能
5. 提交前进行完整测试

## 🔍 故障排除

- 检查SSH连接和权限配置
- 查看日志文件了解详细错误
- 验证软件包脚本语法
- 确认网络连接和下载源可用

---

**注意**: 本系统设计用于Ubuntu/Debian系统，使用前请确保目标服务器满足基本要求。
