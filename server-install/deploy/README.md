# 服务器安装管理系统 - Docker部署指南

简洁的Docker部署配置，支持开发模式热重载的服务器安装管理系统。

## 🚀 一键部署

### 方法1: 使用部署脚本 (推荐)
```bash
cd deploy
./deploy.sh
```

### 方法2: 直接使用 docker-compose
```bash
cd deploy
docker-compose up -d
```

## 📱 访问应用
- 主页面: http://localhost:5000
- 终端页面: http://localhost:5000/terminal

## 🔥 开发模式特性
- ✅ 热重载: 修改代码后自动重启
- ✅ 调试模式: 详细错误信息
- ✅ 代码挂载: 整个项目目录挂载到容器，支持实时同步
- ✅ 简化配置: 无需单独挂载数据目录

## 📁 文件说明
- `Dockerfile` - Docker镜像构建文件
- `docker-compose.yml` - Docker Compose配置
- `deploy.sh` - 一键部署脚本
- `requirements.txt` - Python依赖包
- `test-deployment.sh` - 部署测试脚本

## 🛠️ 常用命令

### 查看日志
```bash
cd deploy
docker-compose logs -f
```

### 停止服务
```bash
cd deploy
docker-compose down
```

### 重启服务
```bash
cd deploy
docker-compose restart
```

### 重新构建
```bash
cd deploy
docker-compose up --build -d
```

### 测试部署
```bash
cd deploy
./test-deployment.sh
```

## 🧪 测试热重载

1. 启动服务后，修改 `../app.py` 中的任意代码
2. 保存文件
3. 观察容器日志，应该看到自动重启信息
4. 刷新浏览器页面验证更改

## 📊 目录结构
```
deploy/
├── Dockerfile              # Docker镜像构建文件
├── docker-compose.yml      # Docker Compose配置
├── deploy.sh               # 一键部署脚本
├── requirements.txt        # Python依赖包
├── test-deployment.sh      # 部署测试脚本
├── logs/                   # 日志目录
├── configs/                # 配置目录
└── packages/               # 软件包目录
```

## 🔧 环境变量
- `FLASK_ENV=development`: Flask 开发模式
- `FLASK_DEBUG=1`: 启用调试
- `DOCKER_MODE=true`: Docker 环境标识
- `PYTHONUNBUFFERED=1`: Python 输出不缓冲

## 🐛 故障排除

### 查看容器状态
```bash
cd deploy
docker-compose ps
```

### 查看详细日志
```bash
cd deploy
docker-compose logs server-install-manager
```

### 进入容器调试
```bash
docker exec -it server-install-manager bash
```

### 清理并重新部署
```bash
cd deploy
docker-compose down
docker-compose up --build -d
```
