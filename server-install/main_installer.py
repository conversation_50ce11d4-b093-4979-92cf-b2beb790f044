#!/usr/bin/env python3
"""
主安装程序
在远程服务器上执行安装队列
"""

import json
import sys
import subprocess
import os
import time
import getpass
from datetime import datetime

def log_message(message, level="INFO"):
    """记录日志消息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] [{level}] {message}")

def check_sources_changed():
    """检查是否已经更换为国内源"""
    try:
        with open('/etc/apt/sources.list', 'r') as f:
            content = f.read()
            return 'mirrors.aliyun.com' in content
    except:
        return False

def auto_change_sources():
    """自动更换为国内源"""
    try:
        log_message("检测到未使用国内源，正在自动更换...")
        script_path = os.path.join(os.path.dirname(__file__), "packages", "common", "change_sources.sh")

        if not os.path.exists(script_path):
            log_message(f"换源脚本不存在: {script_path}", "ERROR")
            return False

        result = subprocess.run(["bash", script_path],
                              capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            log_message("✓ 已自动更换为国内源")
            return True
        else:
            log_message(f"自动换源失败: {result.stderr}", "ERROR")
            return False
    except Exception as e:
        log_message(f"自动换源异常: {e}", "ERROR")
        return False






def check_sudo_nopasswd():
    """检查sudo无密码权限"""
    try:
        result = subprocess.run(["sudo", "-n", "true"],
                              capture_output=True,
                              timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
        return False

def test_sudo_nopasswd_for_user(username, password):
    """测试指定用户的sudo无密码配置"""
    try:
        # 使用sudo -u切换到目标用户，然后测试sudo -n
        test_script = f"""#!/bin/bash
echo '{password}' | sudo -S -u {username} sudo -n whoami &>/dev/null
exit $?
"""
        result = subprocess.run(test_script, shell=True, capture_output=True, timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError):
        return False

def setup_sudo_nopasswd(username=None, password=None):
    """设置sudo无密码权限

    Args:
        username: 用户名，如果为None则自动获取
        password: 密码，如果为None则提示用户输入
    """
    log_message("检查sudo无密码权限...")

    if check_sudo_nopasswd():
        log_message("✓ sudo无密码权限已配置")
        return True

    log_message("⚠ 需要配置sudo无密码权限", "WARNING")
    log_message("正在尝试自动配置sudo无密码权限...")

    # 获取当前用户名
    if username is None:
        current_user = os.environ.get('USER') or os.environ.get('USERNAME')
        if not current_user:
            try:
                current_user = subprocess.check_output(['whoami'], text=True).strip()
            except:
                current_user = 'mxhou'  # 默认用户名
    else:
        current_user = username

    log_message(f"当前用户: {current_user}")

    # 获取密码
    if password is None:
        # 提示用户输入密码
        print(f"\n为了自动安装软件包，需要为用户 {current_user} 配置sudo无密码权限")
        print("请输入当前用户的sudo密码（输入不会显示）:")
        try:
            password = getpass.getpass("密码: ")
        except KeyboardInterrupt:
            log_message("用户取消了密码输入", "WARNING")
            return False
        except Exception as e:
            log_message(f"获取密码时出错: {e}", "ERROR")
            return False

    try:
        # 使用更安全的方式执行sudo配置，避免密码转义问题
        import tempfile

        # 创建临时脚本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as script_file:
            script_content = f"""#!/bin/bash
# 检查用户是否存在
echo "正在检查用户 {current_user} 是否存在..."
if ! id "{current_user}" &>/dev/null; then
    echo "✗ 错误：用户 {current_user} 不存在"
    exit 1
fi
echo "✓ 用户 {current_user} 存在"

# 检查用户是否在sudo组中
echo "正在检查用户 {current_user} 是否在sudo组中..."
if groups {current_user} | grep -q "\\bsudo\\b"; then
    echo "✓ 用户 {current_user} 已在sudo组中"
else
    echo "正在将用户 {current_user} 添加到sudo组..."
    sudo -S usermod -aG sudo {current_user} 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✓ 用户 {current_user} 已添加到sudo组"
    else
        echo "✗ 添加用户到sudo组失败"
        exit 1
    fi
fi

# 备份原始sudoers文件
echo "正在备份sudoers文件..."
sudo -S cp /etc/sudoers /etc/sudoers.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null
echo "✓ 已备份原始sudoers文件"

# 检查是否已经存在该用户的NOPASSWD配置
if sudo -S grep -q "^{current_user}.*NOPASSWD" /etc/sudoers 2>/dev/null; then
    echo "✓ 用户 {current_user} 的NOPASSWD配置已存在"
    exit 0
fi

# 创建临时文件
TEMP_SUDOERS=$(mktemp)

# 复制现有sudoers内容到临时文件
sudo -S cat /etc/sudoers > $TEMP_SUDOERS 2>/dev/null

# 添加NOPASSWD配置到文件末尾
echo "" >> $TEMP_SUDOERS
echo "# Allow {current_user} to run sudo without password" >> $TEMP_SUDOERS
echo "{current_user} ALL=(ALL) NOPASSWD: ALL" >> $TEMP_SUDOERS

# 验证sudoers文件语法
if sudo -S visudo -c -f $TEMP_SUDOERS 2>/dev/null; then
    echo "✓ sudoers文件语法检查通过"
    # 应用新配置
    sudo -S cp $TEMP_SUDOERS /etc/sudoers 2>/dev/null
    echo "✓ sudoers文件已更新"
else
    echo "✗ sudoers文件语法错误，配置失败"
    rm -f $TEMP_SUDOERS
    exit 1
fi

# 清理临时文件
rm -f $TEMP_SUDOERS
"""
            script_file.write(script_content)
            script_file.flush()

            # 使脚本可执行
            os.chmod(script_file.name, 0o755)

            # 执行脚本，通过stdin传递密码
            process = subprocess.Popen(
                ['bash', script_file.name],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 向脚本的stdin发送密码（多次，因为脚本中有多个sudo命令）
            password_input = f"{password}\n" * 10  # 发送多次密码以应对多个sudo命令
            stdout, stderr = process.communicate(input=password_input)

            # 清理临时脚本文件
            os.unlink(script_file.name)

            # 创建结果对象以保持兼容性
            class Result:
                def __init__(self, returncode, stdout, stderr):
                    self.returncode = returncode
                    self.stdout = stdout
                    self.stderr = stderr

            result = Result(process.returncode, stdout, stderr)

        # 显示脚本执行的详细输出
        if result.stdout:
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    log_message(line)

        if result.returncode == 0:
            log_message("✓ sudo无密码权限配置成功")

            # 测试sudo配置是否生效
            log_message("正在测试sudo无密码配置...")
            test_result = test_sudo_nopasswd_for_user(current_user, password)

            if test_result:
                log_message("✓ sudo无密码权限验证成功")
                return True
            else:
                # 再次检查配置是否生效
                if check_sudo_nopasswd():
                    log_message("✓ sudo无密码权限验证成功")
                    return True
                else:
                    log_message("⚠ sudo无密码权限配置完成，但可能需要重新登录才能生效", "WARNING")
                    return True
        else:
            log_message(f"❌ sudo无密码权限配置失败", "ERROR")
            if result.stderr:
                for line in result.stderr.strip().split('\n'):
                    if line.strip():
                        log_message(f"错误: {line}", "ERROR")
            return False

    except Exception as e:
        log_message(f"配置sudo无密码权限时出错: {e}", "ERROR")
        return False

def run_command(command, shell=True, timeout=300):
    """执行系统命令，支持实时输出和交互"""
    try:
        log_message(f"执行命令: {command}")

        # 构建环境变量
        env = os.environ.copy()

        # 如果是sudo命令，确保传递必要的用户信息
        if 'sudo' in command:
            # 获取原始用户信息
            sudo_user = os.environ.get('SUDO_USER', 'mxhou')
            sudo_uid = os.environ.get('SUDO_UID', '1000')
            sudo_gid = os.environ.get('SUDO_GID', '1000')

            # 设置用户相关环境变量，让安装脚本知道真实用户
            env['SUDO_USER'] = sudo_user
            env['SUDO_UID'] = sudo_uid
            env['SUDO_GID'] = sudo_gid
            env['REAL_USER'] = sudo_user
            env['REAL_HOME'] = f'/home/<USER>'

        # 使用实时输出模式，不捕获输出，允许交互
        subprocess.run(
            command,
            shell=shell,
            check=True,
            timeout=timeout,  # 可配置超时时间
            env=env  # 传递修正后的环境变量
        )

        log_message("命令执行成功")
        return True, "", ""

    except subprocess.CalledProcessError as e:
        log_message(f"命令执行失败，返回码: {e.returncode}", "ERROR")
        return False, "", f"命令执行失败，返回码: {e.returncode}"

    except subprocess.TimeoutExpired:
        log_message("命令执行超时", "ERROR")
        return False, "", "命令执行超时"

def install_python_package(package_file):
    """安装Python包"""
    log_message(f"开始安装Python包: {package_file}")

    if not os.path.exists(package_file):
        log_message(f"包文件不存在: {package_file}", "ERROR")
        return False

    # 使用subprocess执行Python安装脚本
    success, stdout, stderr = run_command(f"sudo python3 {package_file}")

    if success:
        log_message(f"Python包安装成功: {package_file}")
    else:
        log_message(f"Python包安装失败: {package_file}", "ERROR")

    return success

def install_shell_package(package_file):
    """安装Shell包"""
    log_message(f"开始安装Shell包: {package_file}")

    if not os.path.exists(package_file):
        log_message(f"包文件不存在: {package_file}", "ERROR")
        return False

    # 确保脚本有执行权限
    run_command(f"chmod +x {package_file}")

    # 使用sudo执行Shell安装脚本
    success, stdout, stderr = run_command(f"sudo bash {package_file}")

    if success:
        log_message(f"Shell包安装成功: {package_file}")
    else:
        log_message(f"Shell包安装失败: {package_file}", "ERROR")

    return success

def uninstall_python_package(package_file):
    """卸载Python包"""
    log_message(f"开始卸载Python包: {package_file}")

    if not os.path.exists(package_file):
        log_message(f"包文件不存在: {package_file}", "ERROR")
        return False

    # 尝试多种卸载参数格式
    uninstall_commands = [
        f"sudo python3 {package_file} uninstall",      # 位置参数
        f"sudo python3 {package_file} --uninstall",    # 选项参数
    ]

    success = False
    for cmd in uninstall_commands:
        log_message(f"尝试执行: {cmd}")
        # 卸载操作可能需要更长时间，设置10分钟超时
        success, stdout, stderr = run_command(cmd, timeout=600)
        if success:
            log_message(f"Python包卸载成功: {package_file}")
            break
        else:
            log_message(f"命令失败，尝试下一个格式...", "WARNING")
            # 如果第一个命令失败，等待一下再尝试下一个
            import time
            time.sleep(3)

    if not success:
        log_message(f"Python包卸载失败: {package_file}", "ERROR")

    return success

def uninstall_shell_package(package_file):
    """卸载Shell包"""
    log_message(f"开始卸载Shell包: {package_file}")

    if not os.path.exists(package_file):
        log_message(f"包文件不存在: {package_file}", "ERROR")
        return False

    run_command(f"chmod +x {package_file}")
    success, stdout, stderr = run_command(f"sudo bash {package_file} uninstall")

    if success:
        log_message(f"Shell包卸载成功: {package_file}")
    else:
        log_message(f"Shell包卸载失败: {package_file}", "ERROR")

    return success

def process_package(package, operation="install"):
    """处理单个包的安装或卸载"""
    package_name = package.get('name', 'Unknown')
    package_file = package.get('file', '')
    package_type = package.get('type', 'python')

    log_message(f"{'安装' if operation == 'install' else '卸载'} {package_name} ({package_type})")

    # 构建完整的包文件路径，使用脚本所在目录作为基准
    script_dir = os.path.dirname(os.path.abspath(__file__))
    full_path = os.path.join(script_dir, 'packages', package_file)

    success = False

    if operation == "install":
        if package_type == 'python':
            success = install_python_package(full_path)
        else:  # shell
            success = install_shell_package(full_path)
    else:  # uninstall
        if package_type == 'python':
            success = uninstall_python_package(full_path)
        else:  # shell
            success = uninstall_shell_package(full_path)

    return success

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 main_installer.py <queue_file> [install|uninstall] [username] [password]")
        print("示例: python3 main_installer.py install_queue.json")
        print("示例: python3 main_installer.py install_queue.json uninstall")
        print("示例: python3 main_installer.py install_queue.json install mxhou mypassword")
        sys.exit(1)

    queue_file = sys.argv[1]
    operation = sys.argv[2] if len(sys.argv) > 2 else "install"
    username = sys.argv[3] if len(sys.argv) > 3 else None
    password = sys.argv[4] if len(sys.argv) > 4 else None

    if operation not in ["install", "uninstall"]:
        log_message("操作必须是 'install' 或 'uninstall'", "ERROR")
        sys.exit(1)

    # 首先检查和配置sudo无密码权限
    log_message("=" * 50)
    log_message("准备安装环境...")

    if not setup_sudo_nopasswd(username, password):
        log_message("❌ sudo无密码权限配置失败，无法继续安装", "ERROR")
        log_message("请手动配置sudo无密码权限或使用sudo运行此脚本", "ERROR")
        sys.exit(1)

    log_message("✓ 安装环境准备完成")

    # 检查是否已经换源，如果没有则自动换源
    if operation == "install":
        log_message("检查软件源配置...")
        if not check_sources_changed():
            log_message("检测到使用官方源，正在自动更换为国内源...")
            if not auto_change_sources():
                log_message("⚠ 自动换源失败，将使用官方源（可能影响下载速度）", "WARNING")
            else:
                log_message("✓ 已自动更换为国内源，下载速度将更快")
        else:
            log_message("✓ 已使用国内源")



    log_message("=" * 50)

    # 检查队列文件是否存在
    if not os.path.exists(queue_file):
        log_message(f"队列文件不存在: {queue_file}", "ERROR")
        sys.exit(1)

    # 读取安装队列
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            queue_data = json.load(f)
    except Exception as e:
        log_message(f"读取队列文件失败: {e}", "ERROR")
        sys.exit(1)

    packages = queue_data.get('packages', [])

    # 如果队列文件中有操作类型，优先使用队列文件中的设置
    queue_operation = queue_data.get('operation', operation)
    if queue_operation in ['install', 'uninstall']:
        operation = queue_operation

    if not packages:
        log_message("队列中没有包需要处理", "WARNING")
        return

    log_message(f"开始{'安装' if operation == 'install' else '卸载'}，共 {len(packages)} 个包")
    log_message("=" * 50)

    # 统计结果
    success_count = 0
    failed_packages = []

    # 如果是卸载，反向处理包列表
    if operation == "uninstall":
        packages = packages[::-1]

    # 处理每个包
    for i, package in enumerate(packages, 1):
        package_name = package.get('name', 'Unknown')

        log_message(f"[{i}/{len(packages)}] 处理包: {package_name}")
        log_message("-" * 30)

        success = process_package(package, operation)

        if success:
            success_count += 1
            log_message(f"✅ {package_name} {'安装' if operation == 'install' else '卸载'}成功")
        else:
            failed_packages.append(package_name)
            log_message(f"❌ {package_name} {'安装' if operation == 'install' else '卸载'}失败", "ERROR")

        # 包之间的间隔
        if i < len(packages):
            log_message("等待 2 秒...")
            time.sleep(2)

        log_message("-" * 30)

    # 输出最终结果
    log_message("=" * 50)
    log_message(f"{'安装' if operation == 'install' else '卸载'}完成！")
    log_message(f"成功: {success_count}/{len(packages)}")

    if failed_packages:
        log_message(f"失败的包: {', '.join(failed_packages)}", "WARNING")
    else:
        log_message("🎉 所有包都处理成功！")

    # 生成结果报告
    report = {
        "operation": operation,
        "timestamp": datetime.now().isoformat(),
        "total_packages": len(packages),
        "success_count": success_count,
        "failed_packages": failed_packages,
        "packages_processed": [pkg.get('name', 'Unknown') for pkg in packages]
    }

    report_file = f"{operation}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        log_message(f"📊 结果报告已保存: {report_file}")
    except Exception as e:
        log_message(f"保存报告失败: {e}", "WARNING")

if __name__ == "__main__":
    main()
