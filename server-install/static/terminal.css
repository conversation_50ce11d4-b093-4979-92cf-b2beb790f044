/* Web终端样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1e1e1e;
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}

.terminal-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #1e1e1e;
}

.terminal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #2d2d2d;
    border-bottom: 1px solid #404040;
    min-height: 50px;
}

.terminal-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 14px;
}

.terminal-icon {
    font-size: 16px;
}

.terminal-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    font-size: 12px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ffa500;
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: #00ff00;
    animation: none;
}

.status-indicator.disconnected {
    background: #ff0000;
    animation: none;
}

.status-indicator.error {
    background: #ff4444;
    animation: none;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.control-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    background: #404040;
    color: #ffffff;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.control-btn:hover:not(:disabled) {
    background: #505050;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 标签页容器 */
.tabs-container {
    background: #2d2d2d;
    border-bottom: 1px solid #404040;
    overflow-x: auto;
    overflow-y: hidden;
}

.tabs-nav {
    display: flex;
    min-height: 40px;
    align-items: flex-end;
}

.tab {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #404040;
    border: 1px solid #555555;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    margin-right: 2px;
    cursor: pointer;
    font-size: 12px;
    color: #cccccc;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    transition: all 0.2s;
}

.tab:hover {
    background: #505050;
}

.tab.active {
    background: #000000;
    color: #ffffff;
    border-color: #666666;
}

.tab-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 8px;
}

.tab-close {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: transparent;
    border: none;
    color: #999999;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s;
}

.tab-close:hover {
    background: #666666;
    color: #ffffff;
}

.tab-status {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #ffa500;
    margin-right: 6px;
    flex-shrink: 0;
}

.tab-status.connected {
    background: #00ff00;
}

.tab-status.disconnected {
    background: #ff0000;
}

.tab-status.error {
    background: #ff4444;
}

/* 终端内容区域 */
.terminal-content {
    flex: 1;
    background: #000000;
    overflow: hidden;
    position: relative;
}

#terminals-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.terminal-wrapper {
    width: 100%;
    height: 100%;
    padding: 10px;
    background: #000000;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    display: none;
}

.terminal-wrapper.active {
    display: block;
}

.terminal-instance {
    width: 100%;
    height: 100%;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: #ffffff;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #404040;
    border-top: 4px solid #00ff00;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal-content {
    background: #2d2d2d;
    border-radius: 8px;
    min-width: 400px;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #404040;
}

.modal-header h3 {
    margin: 0;
    color: #ffffff;
}

.close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.modal-body {
    padding: 20px;
    color: #ffffff;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #404040;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-primary {
    background: #007acc;
    color: #ffffff;
}

.btn-primary:hover {
    background: #005a9e;
}

.btn-secondary {
    background: #404040;
    color: #ffffff;
}

.btn-secondary:hover {
    background: #505050;
}

/* 全屏模式 */
.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .terminal-header {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }

    .terminal-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .control-btn {
        font-size: 11px;
        padding: 5px 10px;
    }

    .modal-content {
        min-width: 300px;
        margin: 20px;
    }
}

/* xterm.js 样式覆盖 */
.xterm {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.2;
}

.xterm-viewport {
    background-color: transparent !important;
}

.xterm-screen {
    background-color: transparent !important;
}