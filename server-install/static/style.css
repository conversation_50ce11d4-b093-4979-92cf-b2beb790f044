/* 服务器安装管理系统样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: white;
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}



/* 通用区域样式 */
section {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

section h3 {
    font-size: 1.1em;
    margin: 0;
    color: #4a5568;
}

/* 区域标题行样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e2e8f0;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.section-actions input[type="text"] {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    width: 180px;
    min-width: 150px;
}

.section-actions button {
    padding: 6px 12px !important;
    font-size: 12px !important;
    border-radius: 6px !important;
    white-space: nowrap;
}

.check-status {
    font-size: 12px;
    color: #666;
    margin-left: 5px;
}

/* 连接状态行样式 */
.connection-status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #e0e0e0;
}

.connection-status-text {
    font-size: 14px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    background: #f8f9fa;
    border-left: 3px solid #e0e0e0;
}

.upload-status-text {
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* 连接状态样式 */
.connection-status-text.success {
    color: #155724;
    background: #d4edda;
    border-left-color: #28a745;
}

.connection-status-text.error {
    color: #721c24;
    background: #f8d7da;
    border-left-color: #dc3545;
}

.connection-status-text.info {
    color: #004085;
    background: #d1ecf1;
    border-left-color: #007bff;
}

/* 安装队列标题样式 */
.queue-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.queue-count-text {
    font-size: 14px;
    color: #666;
    background: #f0f0f0;
    padding: 4px 8px;
    border-radius: 4px;
}

/* 检查状态样式 */
.check-status {
    margin-left: 10px;
    font-size: 0.9em;
    color: #666;
    font-weight: 500;
}

.check-status.checking {
    color: #3182ce;
    animation: pulse 1.5s infinite;
}

.check-status.success {
    color: #38a169;
}

.check-status.error {
    color: #e53e3e;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* 上传提示样式 */
.upload-tips {
    margin-top: 8px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.upload-tips small {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 500;
}

section h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5em;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 10px;
}

/* SSH连接区域 */
.connection-form {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 15px;
    align-items: end;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #4a5568;
}

.input-group input {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    width: 100%;
    height: 48px;
    box-sizing: border-box;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 按钮组样式 */
.button-group {
    display: flex;
    flex-direction: column;
}

/* 水平按钮组样式 */
.button-group-horizontal {
    display: flex;
    flex-direction: row;
    gap: 12px;
    align-items: center;
    justify-content: flex-start;
}

.button-group button {
    padding: 12px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    box-sizing: border-box;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    /* 浅蓝色 */
    color: white;
}

.button-group button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
}

.button-group button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    /* 灰色 */
}



/* 水平按钮组中的按钮样式 */
.button-group-horizontal button {
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    /* 固定宽度确保一致 */
    box-sizing: border-box;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    /* 浅蓝色 */
    color: white;
}

.button-group-horizontal button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
}

.button-group-horizontal button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    /* 灰色 */
}

/* 水平按钮组中的检查状态样式 */
.button-group-horizontal .check-status {
    margin-left: 10px;
    font-size: 12px;
    color: #666;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 响应式布局 - 在小屏幕上改为垂直布局 */
@media (max-width: 768px) {
    .connection-form {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .button-group-horizontal {
        flex-direction: column;
        gap: 8px;
    }

    .button-group-horizontal button {
        width: 100%;
        flex: none;
    }

    .button-group-horizontal .check-status {
        margin-left: 0;
        margin-top: 5px;
        text-align: center;
    }
}



button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    /* 浅蓝色 */
    color: white;
}

button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4);
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    /* 灰色 */
}

/* 状态显示 */
.status {
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    font-weight: 600;
}

.status.success {
    background: #f0fff4;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.status.error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #feb2b2;
}





/* 移除双列布局，改为单列 */

.available-packages,
.installed-packages {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e2e8f0;
    margin-bottom: 20px;
}

.available-header,
.installed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e2e8f0;
}

.available-header h3,
.installed-header h3,
.available-packages h3,
.installed-packages h3 {
    margin: 0;
    color: #4a5568;
    font-size: 1.1em;
}

.available-actions,
.installed-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.available-actions input[type="text"],
.installed-actions input[type="text"] {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    width: 180px;
}

.available-actions button,
.installed-actions button {
    padding: 6px 12px !important;
    font-size: 12px !important;
    border-radius: 6px !important;
}

/* 包列表 */
.packages-container,
.installed-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
}

.packages-list {
    padding: 10px;
}

.package-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    margin-bottom: 6px;
    border: 2px solid transparent;
    border-left: 3px solid #e0e0e0;
    transition: all 0.3s;
    cursor: pointer;
}

.package-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.package-item-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.package-name {
    font-weight: bold;
    font-size: 1.1em;
    color: #333;
}

.package-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.package-status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
}

.install-single-btn {
    padding: 6px 12px !important;
    font-size: 12px !important;
    border-radius: 6px !important;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s !important;
    font-weight: 600 !important;
    min-width: 60px;
}

.install-single-btn:hover:not(:disabled) {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.4) !important;
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%) !important;
}

.install-single-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.check-single-btn {
    padding: 6px 12px !important;
    font-size: 12px !important;
    border-radius: 6px !important;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    /* 浅蓝色 */
    color: white !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s !important;
    font-weight: 600 !important;
    min-width: 60px;
}

.check-single-btn:hover:not(:disabled) {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(116, 185, 255, 0.4) !important;
    background: linear-gradient(135deg, #0984e3 0%, #0770d1 100%) !important;
}

.check-single-btn:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    /* 灰色 */
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.uninstall-single-btn {
    padding: 6px 12px !important;
    font-size: 12px !important;
    border-radius: 6px !important;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: white !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s !important;
    font-weight: 600 !important;
    min-width: 60px;
}

.uninstall-single-btn:hover:not(:disabled) {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4) !important;
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%) !important;
}

.uninstall-single-btn:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    /* 灰色 */
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.package-info {
    grid-column: 1 / -1;
    margin-top: 4px;
}

.package-description {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 6px;
    font-style: italic;
}

.package-tags {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.package-item.selected {
    border-color: #667eea;
    background: #eef2ff;
}

.package-item.checking {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    border-color: #ff9800;
    cursor: not-allowed;
    opacity: 0.8;
}

.checking-status {
    color: #ff9800 !important;
    font-weight: 500;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.6;
    }
}

/* 已安装但可重新安装的包 */
.package-item.installed-available {
    border-color: #28a745;
    background: #d4edda;
    border-left-color: #28a745;
    opacity: 0.9;
}

.package-item.installed-available:hover {
    transform: translateY(-1px);
    border-color: #1e7e34;
    background: #c3e6cb;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* 已安装且已选择的包 */
.package-item.installed-selected {
    border-color: #fd7e14;
    background: #fff3cd;
    border-left-color: #fd7e14;
    opacity: 1;
}

.package-item.installed-selected:hover {
    transform: translateY(-1px);
    border-color: #e55a00;
    background: #ffeaa7;
    box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
}

/* 已选择的包 */
.package-item.selected {
    border-color: #667eea;
    background: #eef2ff;
    border-left-color: #667eea;
}

.package-item.selected .package-item-checkbox {
    accent-color: #667eea;
}

/* 保留原有的已安装样式作为备用 */
.package-item.installed {
    border-color: #28a745;
    background: #d4edda;
    border-left-color: #28a745;
    opacity: 0.8;
}

.package-item.installed:hover {
    transform: none;
    border-color: #28a745;
}

.package-status {
    font-size: 0.8em;
    color: #28a745;
    font-weight: 600;
    margin-left: 8px;
}

.package-name {
    font-weight: 600;
    font-size: 1.1em;
    color: #2d3748;
    margin-bottom: 8px;
}

.package-info {
    display: flex;
    gap: 10px;
    font-size: 0.9em;
    color: #718096;
}

.package-tag {
    background: #e2e8f0;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.8em;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.folder-tag {
    background: #fff3e0;
    color: #f57c00;
}

.platform-tag {
    background: #e3f2fd;
    color: #1976d2;
}

.arch-tag {
    background: #f3e5f5;
    color: #7b1fa2;
}

.type-tag {
    background: #e8f5e8;
    color: #388e3c;
}

.description-tag {
    background: #f5f5f5;
    color: #616161;
    font-style: italic;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.status-tag {
    font-weight: 600;
    border: 1px solid;
}

/* 不同状态的颜色 */
.status-tag.status-running {
    background: #e8f5e8;
    color: #2e7d32;
    border-color: #4caf50;
}

.status-tag.status-installed {
    background: #e3f2fd;
    color: #1976d2;
    border-color: #2196f3;
}

.status-tag.status-stopped {
    background: #fff3e0;
    color: #f57c00;
    border-color: #ff9800;
}

.status-tag.status-failed {
    background: #ffebee;
    color: #d32f2f;
    border-color: #f44336;
}

.status-tag.status-not_installed {
    background: #f5f5f5;
    color: #616161;
    border-color: #9e9e9e;
}

.status-tag.status-unknown {
    background: #f3e5f5;
    color: #7b1fa2;
    border-color: #9c27b0;
}

.status-tag.status-checking {
    background: #e0f2f1;
    color: #00695c;
    border-color: #009688;
}

/* 已安装包列表 */
.installed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.installed-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.installed-actions button {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
}

.verify-btn {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    /* 浅蓝色 */
    color: white !important;
}

.verify-btn:hover:not(:disabled) {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4) !important;
}

.verify-btn:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    /* 灰色 */
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.select-all-btn {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    /* 浅蓝色 */
    color: white !important;
}

.select-all-btn:hover:not(:disabled) {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(116, 185, 255, 0.4) !important;
}

.select-all-btn:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    /* 灰色 */
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.uninstall-selected-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    /* 红色 */
    color: white !important;
}

.uninstall-selected-btn:hover:not(:disabled) {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4) !important;
}

.uninstall-selected-btn:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    /* 灰色 */
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.installed-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    margin-bottom: 6px;
    border: 2px solid transparent;
    border-left: 3px solid #e0e0e0;
    transition: all 0.3s;
    cursor: pointer;
}

.installed-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.installed-item.selected {
    border-color: #667eea;
    background: #eef2ff;
    border-left-color: #667eea;
}

.installed-item-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.installed-item .package-name {
    font-weight: bold;
    font-size: 1.1em;
    color: #333;
}

.installed-item .package-description {
    font-size: 0.9em;
    color: #666;
    margin-top: 4px;
    font-style: italic;
}

.installed-item .package-info {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 8px;
    grid-column: 1 / -1;
}

/* 根据状态设置不同的左边框颜色 */
.installed-item.status-running {
    border-left-color: #28a745;
}

.installed-item.status-installed {
    border-left-color: #007bff;
}

.installed-item.status-stopped {
    border-left-color: #ffc107;
}

.installed-item.status-failed {
    border-left-color: #dc3545;
}

.installed-item.status-unknown {
    border-left-color: #6c757d;
}

.installed-item.status-checking {
    border-left-color: #17a2b8;
    animation: checking-pulse 2s infinite;
}

@keyframes checking-pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.8;
    }

    100% {
        opacity: 1;
    }
}

.installed-item-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.package-name {
    font-weight: bold;
    font-size: 1.1em;
    color: #333;
}

.package-status-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}



.installed-item-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-text {
    font-weight: 500;
    cursor: help;
}



.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-installed {
    background: #28a745;
}

.status-running {
    background: #17a2b8;
}

.status-stopped {
    background: #ffc107;
}

.status-failed {
    background: #dc3545;
}

.status-unknown {
    background: #6c757d;
}

.status-not_installed {
    background: #e9ecef;
}

.status-checking {
    background: #17a2b8;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.remove-btn {
    padding: 6px 12px;
    font-size: 0.85em;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    background: #dc3545;
    color: white;
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}

.check-btn {
    padding: 6px 12px;
    font-size: 0.85em;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    background: #007bff;
    color: white;
    margin-right: 8px;
}

.check-btn:hover {
    background: #0056b3;
    transform: scale(1.05);
}

.empty-installed {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    padding: 40px 20px;
}

/* 安装队列 */
.queue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.queue-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.queue-actions button {
    padding: 8px 16px;
    font-size: 14px;
}

.queue-list {
    min-height: 100px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
}

.queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: move;
    transition: all 0.3s;
    border: 2px solid transparent;
    position: relative;
}

.queue-item:last-child {
    margin-bottom: 0;
}

.queue-item:hover {
    background: #e9ecef;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.queue-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
    z-index: 1000;
}

.queue-item.drag-over {
    border-color: #28a745;
    background: #d4edda;
}

.queue-item .drag-handle {
    cursor: grab;
    color: #6c757d;
    margin-right: 10px;
    font-size: 16px;
}

.queue-item .drag-handle:active {
    cursor: grabbing;
}

.queue-item .item-order {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-right: 10px;
}

.remove-btn {
    background: #e53e3e;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
}

.move-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    min-width: 24px;
    transition: all 0.2s;
}

.move-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.empty-queue {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    padding: 20px;
}

/* 进度指示器 */
.progress-container {
    margin-top: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 4px;
    width: 0%;
    transition: width 0.3s ease;
    animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
    0% {
        background-position: -200px 0;
    }

    100% {
        background-position: 200px 0;
    }
}

.progress-text {
    font-size: 14px;
    color: #4a5568;
    text-align: center;
    font-weight: 500;
}

/* 执行结果 */
.result-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

.tab-btn {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    color: #718096;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.result-text {
    background: #1a202c;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
}

/* 提示框 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background: #38a169;
}

.toast.error {
    background: #e53e3e;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #a0aec0;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .connection-form {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .section-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .section-actions input[type="text"] {
        width: 100%;
        margin-bottom: 8px;
    }

    .available-actions input[type="text"],
    .installed-actions input[type="text"] {
        width: 100%;
        margin-bottom: 8px;
    }

    .available-header,
    .installed-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .available-actions,
    .installed-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .queue-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .connection-status-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

}