// 服务器安装管理系统 - 前端交互逻辑

// 全局状态
let packages = [];
let installQueue = [];
let installedPackages = [];
let filteredInstalledPackages = []; // 已安装软件包的搜索结果
let selectedInstalledPackages = []; // 选中的已安装软件包
let isConnected = false;
let mainSocket = null; // 主页面的WebSocket连接

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    refreshPackages();
    updateQueueDisplay();
    updateInstallButtonState();
    updateUninstallButtonState();
    updateTerminalButtonState();
    updateCheckButtonState();

    // 绑定搜索事件
    document.getElementById('search-input').addEventListener('input', searchPackages);

    // 初始化WebSocket连接用于文件上传进度
    initMainWebSocket();

    // 绑定检查所有按钮的单击和双击事件
    setupCheckAllButtonEvents();

    // 临时调试：显示软件包管理区域以便测试
    if (window.location.search.includes('debug=1')) {
        setPackageManagementVisible(true);
    }
});

// 设置检查所有按钮的事件处理
function setupCheckAllButtonEvents() {
    const checkBtn = document.getElementById('check-btn');
    if (!checkBtn) return;

    let clickTimeout = null;
    let clickCount = 0;

    checkBtn.addEventListener('click', function (e) {
        e.preventDefault();
        clickCount++;

        if (clickCount === 1) {
            // 设置延时，等待可能的第二次点击
            clickTimeout = setTimeout(() => {
                // 单击事件：静默检查
                console.log('单击检查所有按钮 - 静默执行');
                checkAllPackages(false);
                clickCount = 0;
            }, 400); // 400ms内如果有第二次点击则认为是双击
        } else if (clickCount === 2) {
            // 双击事件：弹出终端
            clearTimeout(clickTimeout);
            console.log('双击检查所有按钮 - 弹出终端');
            checkAllPackages(true);
            clickCount = 0;
        }
    });
}

// 显示提示消息
function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 显示/隐藏连接状态行
function showConnectionStatus(show) {
    const statusElement = document.getElementById('connection-status');
    statusElement.style.display = show ? 'block' : 'none';
}

// 更新连接状态
function updateConnectionStatus(text, type = 'info') {
    const statusElement = document.getElementById('connection-status');
    statusElement.textContent = text;
    statusElement.className = `connection-status-text ${type}`;
    showConnectionStatus(true);
}

// 显示/隐藏上传进度文字
function showUploadStatus(show) {
    const uploadStatus = document.getElementById('upload-status-text');
    uploadStatus.style.display = show ? 'block' : 'none';
}

// 更新上传状态文字
function updateUploadStatus(text) {
    const uploadStatus = document.getElementById('upload-status-text');
    uploadStatus.textContent = text;
    showUploadStatus(true);
}

// 更新上传进度 - 文字版本
function updateUploadProgress(percentage, statusText, fileCount, currentFile = '', speed = '', skipped = 0, deleted = 0) {
    // 构建完整的状态文本
    let fullStatusText = statusText;

    if (fileCount && fileCount !== '0/0') {
        fullStatusText += ` (${fileCount})`;
    }

    // 添加同步统计信息
    let syncInfo = [];
    if (skipped > 0) {
        syncInfo.push(`跳过 ${skipped} 个`);
    }
    if (deleted > 0) {
        syncInfo.push(`删除 ${deleted} 个`);
    }
    if (syncInfo.length > 0) {
        fullStatusText += ` [${syncInfo.join(', ')}]`;
    }

    if (speed) {
        fullStatusText += ` - ${speed}`;
    }

    if (currentFile) {
        fullStatusText += ` - ${currentFile}`;
    }

    updateUploadStatus(fullStatusText);
}

// 清除服务器目录
async function clearServerDirectory(host, username, password) {
    updateUploadProgress(5, '正在清除服务器目录...', '0/0');

    const response = await fetch('/api/ssh/clear-directory', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ host, username, password })
    });

    const result = await response.json();
    if (!result.success) {
        throw new Error(`清除目录失败: ${result.message}`);
    }

    updateUploadProgress(10, '服务器目录已清除', '0/0');
    return result;
}

// 开始文件上传
async function startFileUpload(host, username, password) {
    showUploadStatus(true);
    updateUploadProgress(0, '正在准备上传文件...', '0/0');

    try {
        // 先清除服务器目录
        await clearServerDirectory(host, username, password);

        // 监听上传进度事件 - 智能同步版本
        if (mainSocket) {
            mainSocket.on('upload_progress', (data) => {
                updateUploadProgress(
                    data.percentage,
                    data.status,
                    `${data.current}/${data.total}`,
                    data.currentFile,
                    data.speed || '',
                    data.skipped || 0,
                    data.deleted || 0
                );
            });

            mainSocket.on('upload_complete', (data) => {
                if (data.success) {
                    let completeMsg = '智能同步完成';
                    if (data.totalTime && data.avgSpeed) {
                        completeMsg += ` (耗时: ${data.totalTime}, 平均速度: ${data.avgSpeed})`;
                    }
                    updateUploadProgress(100, completeMsg, `${data.totalFiles}/${data.totalFiles}`);
                    setTimeout(() => showUploadStatus(false), 3000);
                } else {
                    updateUploadProgress(0, '同步失败', '0/0', data.error);
                    setTimeout(() => showUploadStatus(false), 3000);
                }
            });
        }

        // 发起上传请求
        const uploadResponse = await fetch('/api/ssh/upload-with-progress', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ host, username, password })
        });

        const uploadResult = await uploadResponse.json();
        if (!uploadResult.success) {
            throw new Error(`上传失败: ${uploadResult.message}`);
        }

        return uploadResult;
    } catch (error) {
        updateUploadProgress(0, '操作失败', '0/0', error.message);
        setTimeout(() => showUploadStatus(false), 3000);
        throw error;
    }
}

// 测试SSH连接
async function testConnection() {
    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    if (!host || !username || !password) {
        showToast('请填写完整的连接信息', 'error');
        return;
    }

    const connectBtn = document.getElementById('connect-btn');

    connectBtn.disabled = true;
    connectBtn.textContent = '🔄 连接中...';
    updateConnectionStatus('🔄 正在连接...', 'info');

    try {
        const response = await fetch('/api/ssh/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ host, username, password })
        });

        const result = await response.json();

        if (result.success) {
            // 隐藏连接状态显示
            showConnectionStatus(false);
            isConnected = true;

            // 只在控制台显示诊断信息，不显示状态条
            if (result.diagnostic && result.diagnostic.steps) {
                console.log('连接诊断信息:', result.diagnostic);
            }

            // 简单的成功提示
            showToast('SSH连接成功！', 'success');

            // 连接成功后自动上传脚本并执行完整初始化流程
            setTimeout(async () => {
                try {
                    // 1. 上传完整脚本
                    showToast('正在上传脚本...', 'info');
                    await startFileUpload(host, username, password);
                    showToast('脚本上传成功！', 'success');

                    // 2. 显示包管理区域
                    setPackageManagementVisible(true);

                    // 3. 清除待安装列表
                    installQueue = [];
                    updateQueueDisplay();

                    // 4. 执行包状态检查（包含包列表刷新）
                    showToast('正在检查包状态...', 'info');
                    await checkAllPackages();

                    showToast('🎉 系统初始化完成！可以开始选择软件包', 'success');

                } catch (error) {
                    showToast('初始化失败: ' + error.message, 'error');
                    // 失败时隐藏包管理区域
                    setPackageManagementVisible(false);
                } finally {
                    // 更新按钮状态
                    updateInstallButtonState();
                    updateTerminalButtonState();
                    updateCheckButtonState();
                }
            }, 500);
        } else {
            updateConnectionStatus('❌ ' + result.message, 'error');
            isConnected = false;
            setPackageManagementVisible(false);
            updateInstallButtonState();
            updateTerminalButtonState();
            updateCheckButtonState();

            // 显示详细诊断信息
            if (result.diagnostic && result.diagnostic.steps) {
                console.log('连接失败诊断信息:', result.diagnostic);
                let diagnosticMsg = '连接诊断:\n' + result.diagnostic.steps.join('\n');
                showToast(result.message + '\n\n' + diagnosticMsg, 'error');
            } else {
                showToast(result.message, 'error');
            }
        }
    } catch (error) {
        updateConnectionStatus('❌ 连接失败: ' + error.message, 'error');
        isConnected = false;
        setPackageManagementVisible(false);
        updateInstallButtonState();
        updateTerminalButtonState();
        updateCheckButtonState();
        showToast('连接失败: ' + error.message, 'error');
    } finally {
        connectBtn.disabled = false;
        connectBtn.textContent = '🔗 连接';
    }
}

// 更新安装按钮状态
function updateInstallButtonState() {
    const installBtn = document.getElementById('install-btn');
    // 当SSH连接成功且安装队列非空时，启用安装按钮
    installBtn.disabled = !(isConnected && installQueue.length > 0);
}

// 更新终端按钮状态
function updateTerminalButtonState() {
    const terminalBtn = document.getElementById('terminal-btn');
    // 当SSH连接成功时，启用终端按钮
    terminalBtn.disabled = !isConnected;
}

// 更新检查按钮状态
function updateCheckButtonState() {
    const checkBtn = document.getElementById('check-btn');
    // 当SSH连接成功时，启用检查按钮
    checkBtn.disabled = !isConnected;
}

// 打开终端
function openTerminal() {
    if (!isConnected) {
        showToast('请先测试SSH连接', 'error');
        return;
    }

    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // 构建终端URL，传递连接参数
    const terminalUrl = `/terminal?host=${encodeURIComponent(host)}&username=${encodeURIComponent(username)}&password=${encodeURIComponent(btoa(password))}`;

    // 在新窗口打开终端
    window.open(terminalUrl, '_blank', 'width=1000,height=600,scrollbars=yes,resizable=yes');
}

// 显示/隐藏包管理区域
function setPackageManagementVisible(visible) {
    const packageSection = document.getElementById('package-management-section');
    const queueSection = document.getElementById('install-queue-section');

    if (visible) {
        packageSection.style.display = 'block';
        queueSection.style.display = 'block';
        // 显示区域后立即更新包数量
        setTimeout(() => {
            updatePackageCount(packages.length, packages.length);
        }, 100);
    } else {
        packageSection.style.display = 'none';
        queueSection.style.display = 'none';
    }
}

// 禁用/启用包列表选择（优化版）
function setPackageListEnabled(enabled) {
    const packagesList = document.getElementById('packages-list');
    const packages = packagesList.querySelectorAll('.package-item');

    packages.forEach(pkg => {
        if (!enabled) {
            // 禁用时：移除点击事件，添加禁用样式
            pkg.style.pointerEvents = 'none';
            pkg.style.opacity = '0.6';
            pkg.classList.add('checking');

            // 如果有状态文本，更新为检查中
            const statusSpan = pkg.querySelector('.package-status');
            if (!statusSpan && !pkg.classList.contains('installed')) {
                const nameDiv = pkg.querySelector('.package-name');
                nameDiv.innerHTML += '<span class="package-status checking-status">检查中...</span>';
            }
        } else {
            // 启用时：恢复点击事件和样式
            pkg.style.pointerEvents = 'auto';
            pkg.style.opacity = '1';
            pkg.classList.remove('checking');

            // 移除"检查中"状态
            const checkingStatus = pkg.querySelector('.checking-status');
            if (checkingStatus) {
                checkingStatus.remove();
            }
        }
    });

    // 同时禁用/启用筛选器和刷新按钮
    const osFilter = document.getElementById('os-filter');
    const archFilter = document.getElementById('arch-filter');
    if (osFilter) osFilter.disabled = !enabled;
    if (archFilter) archFilter.disabled = !enabled;

    // 禁用刷新按钮
    const refreshBtns = document.querySelectorAll('button[onclick*="refresh"]');
    refreshBtns.forEach(btn => {
        btn.disabled = !enabled;
    });
}

// 原有的更新进度文本函数已移除



// 刷新包列表
async function refreshPackages() {
    const packagesList = document.getElementById('packages-list');
    packagesList.innerHTML = '<div class="loading">正在加载包列表...</div>';

    try {
        const response = await fetch('/api/packages');
        packages = await response.json();

        if (packages.length === 0) {
            packagesList.innerHTML = '<div class="loading">暂无可用的软件包</div>';
            updatePackageCount(0, 0);
        } else {
            searchPackages(); // 应用当前搜索条件
        }
    } catch (error) {
        packagesList.innerHTML = '<div class="loading">加载失败: ' + error.message + '</div>';
        showToast('加载包列表失败', 'error');
        updatePackageCount(0, 0);
    }
}

// 显示包列表
function displayPackages(packagesToShow) {
    const packagesList = document.getElementById('packages-list');

    if (packagesToShow.length === 0) {
        packagesList.innerHTML = '<div class="loading">没有符合条件的软件包</div>';
        return;
    }

    packagesList.innerHTML = packagesToShow.map(pkg => {
        const isInstalled = isPackageInstalled(pkg);
        const inQueue = isInQueue(pkg);

        // 获取已安装包的状态信息
        const installedPkg = installedPackages.find(installed => installed.file === pkg.file);
        const packageStatus = installedPkg ? installedPkg.status : 'not_installed';

        // 确定状态类
        let statusClass = '';

        if (isInstalled && inQueue) {
            statusClass = 'installed-selected';
        } else if (isInstalled) {
            statusClass = 'installed-available';
        } else if (inQueue) {
            statusClass = 'selected';
        }

        return `
            <div class="package-item ${statusClass}" onclick="togglePackage('${pkg.file}')">
                <input type="checkbox" class="package-item-checkbox"
                       ${inQueue ? 'checked' : ''}
                       onchange="event.stopPropagation()"
                       data-file="${pkg.file}">
                <div class="package-name">
                    ${pkg.name}
                </div>
                <div class="package-actions">
                    ${isInstalled ? `
                        <button class="check-single-btn"
                                onclick="event.stopPropagation(); checkSinglePackage('${pkg.file}')"
                                title="检查此软件包状态">
                            🔍 检查
                        </button>
                        <button class="uninstall-single-btn"
                                onclick="event.stopPropagation(); uninstallSinglePackage('${pkg.file}')"
                                title="卸载此软件包">
                            🗑️ 卸载
                        </button>
                    ` : `
                        <button class="install-single-btn"
                                onclick="event.stopPropagation(); installSinglePackage('${pkg.file}')"
                                title="直接安装此软件包">
                            🚀 安装
                        </button>
                    `}
                </div>
                <div class="package-info">
                    <div class="package-tags">
                        <span class="package-tag folder-tag">📁 ${pkg.folder || 'unknown'}</span>
                        <span class="package-tag platform-tag">💻 ${pkg.os}</span>
                        <span class="package-tag arch-tag">🏗️ ${pkg.arch}</span>
                        <span class="package-tag type-tag">📄 ${pkg.type}</span>
                        ${pkg.description && pkg.description !== '暂无描述' ? `<span class="package-tag description-tag">📝 ${pkg.description}</span>` : ''}
                        <span class="package-tag status-tag status-${packageStatus}">${getStatusTextPlain(packageStatus)}</span>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// 搜索包列表
function searchPackages() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase().trim();

    let filtered = packages;

    if (searchTerm) {
        filtered = packages.filter(pkg => {
            // 获取包的安装状态文字
            const installedPkg = installedPackages.find(installed => installed.file === pkg.file);
            const packageStatus = installedPkg ? installedPkg.status : 'not_installed';
            const statusText = getStatusTextPlain(packageStatus);

            // 基本字段搜索（包括状态文字）
            const basicMatch = pkg.name.toLowerCase().includes(searchTerm) ||
                pkg.file.toLowerCase().includes(searchTerm) ||
                pkg.os.toLowerCase().includes(searchTerm) ||
                pkg.arch.toLowerCase().includes(searchTerm) ||
                pkg.type.toLowerCase().includes(searchTerm) ||
                (pkg.folder && pkg.folder.toLowerCase().includes(searchTerm)) ||
                (pkg.description && pkg.description.toLowerCase().includes(searchTerm)) ||
                statusText.toLowerCase().includes(searchTerm);

            // 架构和平台别名搜索
            const aliases = {
                // 架构别名
                'x86': ['x86_64'],
                '64': ['x86_64'],
                '86': ['x86_64'],
                'x64': ['x86_64'],
                'intel': ['x86_64'],
                'amd64': ['x86_64'],
                'arm64': ['arm'],
                'aarch64': ['arm'],
                '通用': ['any'],
                'universal': ['any'],
                'all': ['any'],
                // 平台别名
                'linux': ['ubuntu', 'centos', 'debian'],
                'redhat': ['centos'],
                'rhel': ['centos'],
                'deb': ['debian'],
                'win': ['windows']
            };

            let aliasMatch = false;
            if (aliases[searchTerm]) {
                aliasMatch = aliases[searchTerm].some(alias =>
                    pkg.arch.toLowerCase().includes(alias.toLowerCase()) ||
                    pkg.os.toLowerCase().includes(alias.toLowerCase())
                );
            }

            return basicMatch || aliasMatch;
        });
    }

    // 按首字母顺序排序
    filtered.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));

    displayPackages(filtered);
    updatePackageCount(packages.length, filtered.length);
}

// 更新包数量显示
function updatePackageCount(totalCount, filteredCount) {
    const packageManagementTitle = document.querySelector('#package-management-section .section-header h3');
    if (packageManagementTitle) {
        const searchTerm = document.getElementById('search-input').value.toLowerCase().trim();
        const installedCount = installedPackages.length;

        if (searchTerm) {
            packageManagementTitle.textContent = `📦 软件包管理 (显示 ${filteredCount} / 总计 ${totalCount} 已安装 ${installedCount})`;
        } else {
            packageManagementTitle.textContent = `📦 软件包管理 (总计 ${totalCount} 已安装 ${installedCount})`;
        }
    }
}

// 清空搜索
function clearSearch() {
    document.getElementById('search-input').value = '';
    searchPackages();
}

// 全选搜索结果
function selectAllSearchResults() {
    const searchTerm = document.getElementById('search-input').value.toLowerCase().trim();

    // 获取当前搜索结果
    let filtered = packages;
    if (searchTerm) {
        filtered = packages.filter(pkg => {
            // 基本字段搜索
            const basicMatch = pkg.name.toLowerCase().includes(searchTerm) ||
                pkg.file.toLowerCase().includes(searchTerm) ||
                pkg.os.toLowerCase().includes(searchTerm) ||
                pkg.arch.toLowerCase().includes(searchTerm) ||
                pkg.type.toLowerCase().includes(searchTerm) ||
                (pkg.folder && pkg.folder.toLowerCase().includes(searchTerm)) ||
                (pkg.description && pkg.description.toLowerCase().includes(searchTerm));

            // 架构和平台别名搜索
            const aliases = {
                // 架构别名
                'x86': ['x86_64'],
                '64': ['x86_64'],
                '86': ['x86_64'],
                'x64': ['x86_64'],
                'intel': ['x86_64'],
                'amd64': ['x86_64'],
                'arm64': ['arm'],
                'aarch64': ['arm'],
                '通用': ['any'],
                'universal': ['any'],
                'all': ['any'],
                // 平台别名
                'linux': ['ubuntu', 'centos', 'debian'],
                'redhat': ['centos'],
                'rhel': ['centos'],
                'deb': ['debian'],
                'win': ['windows']
            };

            let aliasMatch = false;
            if (aliases[searchTerm]) {
                aliasMatch = aliases[searchTerm].some(alias =>
                    pkg.arch.toLowerCase().includes(alias.toLowerCase()) ||
                    pkg.os.toLowerCase().includes(alias.toLowerCase())
                );
            }

            return basicMatch || aliasMatch;
        });
    }

    // 只过滤掉已经在队列中的包，允许已安装的包被重新选择
    const availablePackages = filtered.filter(pkg => !isInQueue(pkg));

    if (availablePackages.length === 0) {
        showToast('没有可选择的搜索结果', 'error');
        return;
    }

    // 将所有可用的搜索结果添加到安装队列
    availablePackages.forEach(pkg => {
        if (!isInQueue(pkg)) {
            installQueue.push(pkg);
        }
    });

    updateQueueDisplay();
    searchPackages(); // 重新渲染以更新选中状态
    showToast(`已选择 ${availablePackages.length} 个搜索结果`);
}







// 更新卸载按钮状态
function updateUninstallButtonState() {
    // 获取可用包列表中选中的已安装包数量
    const selectedCheckboxes = document.querySelectorAll('.package-item-checkbox:checked');
    let selectedInstalledCount = 0;

    selectedCheckboxes.forEach(checkbox => {
        const file = checkbox.dataset.file;
        const isInstalled = installedPackages.some(installed => installed.file === file);
        if (isInstalled) {
            selectedInstalledCount++;
        }
    });

    const uninstallBtn = document.querySelector('.uninstall-selected-btn');
    if (uninstallBtn) {
        uninstallBtn.disabled = selectedInstalledCount === 0;
        uninstallBtn.textContent = selectedInstalledCount > 0
            ? `🗑️ 卸载选中 (${selectedInstalledCount})`
            : '🗑️ 卸载选中';
    }
}

// 检查包是否在队列中
function isInQueue(pkg) {
    return installQueue.some(item => item.file === pkg.file);
}

// 检查包是否已安装
function isPackageInstalled(pkg) {
    return installedPackages.some(item => item.file === pkg.file);
}

// 切换包选择状态
function togglePackage(file) {
    const pkg = packages.find(p => p.file === file);
    if (!pkg) return;

    const index = installQueue.findIndex(item => item.file === file);
    const checkbox = document.querySelector(`input[data-file="${file}"]`);

    if (index >= 0) {
        // 从队列中移除
        installQueue.splice(index, 1);
        if (checkbox) checkbox.checked = false;
    } else {
        // 添加到队列
        installQueue.push(pkg);
        if (checkbox) checkbox.checked = true;
    }

    updateQueueDisplay();
    updateUninstallButtonState(); // 更新卸载按钮状态
    searchPackages(); // 重新渲染以更新选中状态
}

// 更新队列显示
function updateQueueDisplay() {
    const queueCount = document.getElementById('queue-count');
    const queueList = document.getElementById('queue-list');

    queueCount.textContent = installQueue.length;

    if (installQueue.length === 0) {
        queueList.innerHTML = '<div class="empty-queue">暂无选择的包</div>';
    } else {
        queueList.innerHTML = installQueue.map((pkg, index) => `
            <div class="queue-item" draggable="true" data-index="${index}" data-file="${pkg.file}">
                <div style="display: flex; align-items: center;">
                    <div class="item-order">${index + 1}</div>
                    <div class="drag-handle">⋮⋮</div>
                    <span>${pkg.name} (${pkg.os}/${pkg.arch})</span>
                </div>
                <div style="display: flex; gap: 5px;">
                    ${index > 0 ? `<button class="move-btn" onclick="moveQueueItem('${pkg.file}', 'up')" title="上移">↑</button>` : ''}
                    ${index < installQueue.length - 1 ? `<button class="move-btn" onclick="moveQueueItem('${pkg.file}', 'down')" title="下移">↓</button>` : ''}
                    <button class="remove-btn" onclick="removeFromQueue('${pkg.file}')">移除</button>
                </div>
            </div>
        `).join('');

        // 添加拖拽事件监听器
        addDragListeners();
    }

    // 更新安装按钮状态
    updateInstallButtonState();
}

// 从队列中移除包
function removeFromQueue(file) {
    const index = installQueue.findIndex(item => item.file === file);
    if (index >= 0) {
        installQueue.splice(index, 1);
        updateQueueDisplay();
        searchPackages(); // 重新渲染以更新选中状态
    }
}

// 清空队列
function clearQueue() {
    if (installQueue.length === 0) return;

    if (confirm('确定要清空安装队列吗？')) {
        installQueue = [];
        updateQueueDisplay();
        searchPackages();
    }
}

// 全选未安装的包
function selectAllUninstalled() {
    // 获取所有未安装的包
    const uninstalledPackages = packages.filter(pkg => !isPackageInstalled(pkg));

    if (uninstalledPackages.length === 0) {
        showToast('没有未安装的软件包', 'error');
        return;
    }

    // 过滤掉已经在队列中的包
    const availableUninstalled = uninstalledPackages.filter(pkg => !isInQueue(pkg));

    if (availableUninstalled.length === 0) {
        showToast('所有未安装的包都已在队列中', 'info');
        return;
    }

    // 将所有未安装且不在队列中的包添加到安装队列
    availableUninstalled.forEach(pkg => {
        installQueue.push(pkg);
    });

    updateQueueDisplay();
    searchPackages(); // 重新渲染以更新选中状态
    showToast(`已选择 ${availableUninstalled.length} 个未安装的包`);
}

// 安装单个软件包
async function installSinglePackage(file) {
    const pkg = packages.find(p => p.file === file);
    if (!pkg) {
        showToast('找不到指定的软件包', 'error');
        return;
    }

    if (!isConnected) {
        showToast('请先测试SSH连接', 'error');
        return;
    }

    await executeInstall([pkg]);
}

// 执行安装操作
async function executeInstall(packagesToInstall) {
    if (!isConnected) {
        showToast('请先测试SSH连接', 'error');
        return;
    }

    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        // 首先准备安装队列文件
        const response = await fetch('/api/install/prepare', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                host,
                username,
                password,
                packages: packagesToInstall
            })
        });

        const result = await response.json();

        if (result.success) {
            // 构建安装命令，包含用户名和密码参数以避免手动输入
            const installCommand = `cd /home/<USER>/server_install && python3 main_installer.py install_queue.json install "${username}" "${password}"`;

            // 将包信息编码为JSON字符串
            const packagesJson = encodeURIComponent(JSON.stringify(packagesToInstall));

            // 构建终端URL，包含要执行的命令、操作类型和包信息
            const terminalUrl = `/terminal?host=${encodeURIComponent(host)}&username=${encodeURIComponent(username)}&password=${encodeURIComponent(btoa(password))}&command=${encodeURIComponent(installCommand)}&operation=install&packages=${packagesJson}`;

            // 在新窗口打开终端并执行安装命令
            window.open(terminalUrl, '_blank', 'width=1200,height=700,scrollbars=yes,resizable=yes');

            showToast('已在新终端窗口中开始安装，请查看终端输出');

        } else {
            showToast(result.message || '准备安装失败', 'error');
        }
    } catch (error) {
        showToast('准备安装失败: ' + error.message, 'error');
    }
}

// 开始安装
async function startInstall() {
    if (installQueue.length === 0) {
        showToast('请先选择要安装的软件包', 'error');
        return;
    }

    // 保存安装队列副本
    const packagesToInstall = [...installQueue];

    // 执行安装
    await executeInstall(packagesToInstall);

    // 清空安装队列
    installQueue = [];
    updateQueueDisplay();
    searchPackages();
}

// 原有的显示安装进度和结果的函数已移除
// 安装过程现在在终端中实时显示

// 旧的卸载功能已移除，现在使用已安装包列表进行卸载

// 原有的切换结果标签函数已移除

// 保存配置
function saveConfig() {
    if (installQueue.length === 0) {
        showToast('没有可保存的配置', 'error');
        return;
    }

    const config = {
        name: prompt('请输入配置名称:', 'default') || 'default',
        packages: installQueue,
        timestamp: new Date().toISOString()
    };

    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `${config.name}.json`;
    link.click();

    showToast('配置已保存');
}

// 加载配置
function loadConfig() {
    const fileInput = document.getElementById('config-file');
    const file = fileInput.files[0];

    if (!file) return;

    const reader = new FileReader();
    reader.onload = function (e) {
        try {
            const config = JSON.parse(e.target.result);

            if (config.packages && Array.isArray(config.packages)) {
                installQueue = config.packages;
                updateQueueDisplay();
                searchPackages();
                showToast(`已加载配置: ${config.name || 'default'}`);
            } else {
                showToast('配置文件格式错误', 'error');
            }
        } catch (error) {
            showToast('配置文件解析失败', 'error');
        }
    };

    reader.readAsText(file);
}

// 拖拽功能实现
let draggedElement = null;
let draggedIndex = null;

function addDragListeners() {
    const queueItems = document.querySelectorAll('.queue-item');

    queueItems.forEach((item, index) => {
        // 拖拽开始
        item.addEventListener('dragstart', function (e) {
            draggedElement = this;
            draggedIndex = parseInt(this.dataset.index);
            this.classList.add('dragging');

            // 设置拖拽数据
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.outerHTML);
        });

        // 拖拽结束
        item.addEventListener('dragend', function (e) {
            this.classList.remove('dragging');
            draggedElement = null;
            draggedIndex = null;

            // 清除所有拖拽样式
            queueItems.forEach(item => {
                item.classList.remove('drag-over');
            });
        });

        // 拖拽进入
        item.addEventListener('dragenter', function (e) {
            e.preventDefault();
            if (this !== draggedElement) {
                this.classList.add('drag-over');
            }
        });

        // 拖拽离开
        item.addEventListener('dragleave', function (e) {
            this.classList.remove('drag-over');
        });

        // 拖拽悬停
        item.addEventListener('dragover', function (e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        });

        // 放置
        item.addEventListener('drop', function (e) {
            e.preventDefault();
            this.classList.remove('drag-over');

            if (this !== draggedElement) {
                const dropIndex = parseInt(this.dataset.index);

                // 重新排列数组
                const draggedItem = installQueue[draggedIndex];
                installQueue.splice(draggedIndex, 1);
                installQueue.splice(dropIndex, 0, draggedItem);

                // 更新显示
                updateQueueDisplay();
                searchPackages(); // 更新包列表选中状态

                showToast('安装顺序已调整');
            }
        });
    });
}

// 添加上下移动按钮功能
function moveQueueItem(file, direction) {
    const index = installQueue.findIndex(item => item.file === file);
    if (index === -1) return;

    let newIndex;
    if (direction === 'up' && index > 0) {
        newIndex = index - 1;
    } else if (direction === 'down' && index < installQueue.length - 1) {
        newIndex = index + 1;
    } else {
        return; // 无法移动
    }

    // 交换位置
    const item = installQueue[index];
    installQueue[index] = installQueue[newIndex];
    installQueue[newIndex] = item;

    updateQueueDisplay();
    searchPackages();
    showToast('安装顺序已调整');
}

// ==================== 已安装包管理 ====================

// 更新检查状态显示（使用上传状态区域）
function updateCheckStatusText(text) {
    updateUploadStatus(text);
}

// 从终端窗口调用的刷新包列表函数
function refreshPackageListAfterCheck() {
    console.log('收到终端窗口的刷新请求');

    if (!isConnected) {
        console.log('未连接到服务器，跳过刷新');
        return;
    }

    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // 显示刷新状态
    updateCheckStatusText('📡 正在刷新包列表...');
    showUploadStatus(true);

    // 调用后台检查API获取最新状态
    fetch('/api/packages/check-all', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            host,
            username,
            password
        })
    })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 更新已安装包列表
                installedPackages = result.installedPackages || [];

                // 更新显示
                updateInstalledDisplay(installedPackages);
                searchPackages(); // 更新可安装包列表显示

                // 显示刷新成功状态
                const totalPackages = result.totalPackages || packages.length;
                const installedCount = installedPackages.length;
                const uninstalledCount = totalPackages - installedCount;
                const checkDuration = result.checkDurationMs || 0;

                updateCheckStatusText(`✅ 包列表已刷新！总包数: ${totalPackages}, 已安装: ${installedCount}, 未安装: ${uninstalledCount} (检查用时: ${checkDuration}ms)`);

                showToast(`🔄 包列表已刷新！找到 ${totalPackages} 个可安装包，${installedCount} 个已安装包 (检查用时: ${checkDuration}ms)`, 'success');

                // 3秒后隐藏状态
                setTimeout(() => showUploadStatus(false), 3000);
            } else {
                updateCheckStatusText('❌ 刷新包列表失败');
                showToast('❌ 刷新包列表失败: ' + result.message, 'error');
                setTimeout(() => showUploadStatus(false), 3000);
            }
        })
        .catch(error => {
            console.error('刷新包列表失败:', error);
            updateCheckStatusText('❌ 刷新包列表失败');
            showToast('❌ 刷新包列表失败: ' + error.message, 'error');
            setTimeout(() => showUploadStatus(false), 3000);
        });
}

// 检查所有包状态 - 后台执行并更新状态
async function checkAllPackages(showTerminal = false) {
    if (!isConnected) {
        showToast('请先连接服务器', 'error');
        return;
    }

    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // 如果需要显示终端，打开终端窗口
    if (showTerminal) {
        const checkAllCommand = `cd /home/<USER>/server_install && python3 packages/common/check_all_packages.py`;
        const terminalUrl = `/terminal?host=${encodeURIComponent(host)}&username=${encodeURIComponent(username)}&password=${encodeURIComponent(btoa(password))}&command=${encodeURIComponent(checkAllCommand)}&operation=check_all`;
        window.open(terminalUrl, '_blank', 'width=1200,height=700,scrollbars=yes,resizable=yes');
        showToast('已在新终端窗口中开始检查所有包状态，检查完成后将自动刷新包列表');
        return;
    }

    // 后台执行检查并更新状态
    const checkBtn = document.getElementById('check-btn');
    let startTime = Date.now();
    let timingInterval;

    // 更新UI状态
    if (checkBtn) {
        checkBtn.disabled = true;
        checkBtn.textContent = '🔄 检查中...';
    }

    // 开始计时显示
    updateCheckStatusText('🔍 正在检查包状态... (0ms)');
    showUploadStatus(true);

    timingInterval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        updateCheckStatusText(`🔍 正在检查包状态... (${elapsed}ms)`);
    }, 100);

    try {
        // 后台执行检查脚本
        const response = await fetch('/api/packages/check-all', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                host,
                username,
                password
            })
        });

        const result = await response.json();
        const duration = Date.now() - startTime;

        // 停止计时
        clearInterval(timingInterval);

        if (result.success) {
            // 更新已安装包列表
            installedPackages = result.installedPackages || [];

            // 更新显示
            updateInstalledDisplay(installedPackages);
            searchPackages(); // 更新可安装包列表显示

            // 显示成功状态和结果
            const totalPackages = result.totalPackages || packages.length;
            const installedCount = installedPackages.length;
            const uninstalledCount = totalPackages - installedCount;

            updateCheckStatusText(`✅ 检查完成！总包数: ${totalPackages}, 已安装: ${installedCount}, 未安装: ${uninstalledCount} (耗时: ${duration}ms)`);

            showToast(`🎉 检查完成！找到 ${totalPackages} 个可安装包，${installedCount} 个已安装包 (耗时: ${duration}ms)`, 'success');

            // 3秒后隐藏状态
            setTimeout(() => showUploadStatus(false), 3000);
        } else {
            updateCheckStatusText(`❌ 检查失败 (耗时: ${duration}ms)`);
            showToast('❌ 检查包状态失败: ' + result.message, 'error');
            setTimeout(() => showUploadStatus(false), 3000);
        }
    } catch (error) {
        const duration = Date.now() - startTime;
        clearInterval(timingInterval);

        console.error('检查包状态失败:', error);
        updateCheckStatusText(`❌ 检查失败 (耗时: ${duration}ms)`);
        showToast('❌ 检查包状态失败: ' + error.message, 'error');
        setTimeout(() => showUploadStatus(false), 3000);
    } finally {
        // 恢复按钮状态
        if (checkBtn) {
            checkBtn.disabled = false;
            checkBtn.textContent = '🔍 检查所有';
        }
    }
}

// 刷新已安装包列表（保留原函数供其他地方调用）
async function refreshInstalledPackages() {
    if (!isConnected) {
        updateInstalledDisplay([]);
        return;
    }

    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        const response = await fetch('/api/packages/installed', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ host, username, password })
        });

        const result = await response.json();

        if (result.success) {
            installedPackages = result.packages || [];
            updateInstalledDisplay(installedPackages);
            updateUninstallButtonState(); // 更新卸载按钮状态
            searchPackages(); // 重新渲染可用包列表以更新状态
        } else {
            console.error('获取已安装包列表失败:', result.message);
            updateInstalledDisplay([]);
            updateUninstallButtonState();
        }
    } catch (error) {
        console.error('获取已安装包列表失败:', error);
        updateInstalledDisplay([]);
        updateUninstallButtonState();
    }
}

// 更新已安装包数据（不再显示独立的已安装包列表）
function updateInstalledDisplay(packages) {
    installedPackages = packages;
    filteredInstalledPackages = [...packages];
    // 已安装包现在显示在可用包列表中，不需要单独显示
}



// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'running': '🟢 运行中',
        'installed': '🔵 已安装',
        'stopped': '🟡 已停止',
        'failed': '🔴 安装失败',
        'not_installed': '⚪ 未安装',
        'unknown': '❓ 状态未知',
        'checking': '🔄 检查中...'
    };
    return statusMap[status] || '❓ 未知状态';
}

// 获取纯文字状态文本（用于标签显示和搜索）
function getStatusTextPlain(status) {
    const statusMap = {
        'running': '运行中',
        'installed': '已安装',
        'stopped': '已停止',
        'failed': '安装失败',
        'not_installed': '未安装',
        'unknown': '状态未知',
        'checking': '检查中'
    };
    return statusMap[status] || '未知状态';
}

// 获取状态详细信息
function getStatusDetails(status) {
    const detailsMap = {
        'running': '软件包已安装并正在运行',
        'installed': '软件包已安装但未运行',
        'stopped': '软件包已安装但已停止',
        'failed': '软件包安装失败',
        'not_installed': '软件包未安装',
        'unknown': '无法确定软件包状态',
        'checking': '正在检查软件包状态...'
    };
    return detailsMap[status] || '状态信息不可用';
}

// 获取状态优先级（用于排序）
function getStatusPriority(status) {
    const priorityMap = {
        'running': 1,
        'installed': 2,
        'stopped': 3,
        'failed': 4,
        'unknown': 5,
        'not_installed': 6,
        'checking': 7
    };
    return priorityMap[status] || 8;
}



// 验证单个包状态
async function verifyPackageStatus(file) {
    const pkg = installedPackages.find(p => p.file === file);
    if (!pkg) return;

    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // 显示检查状态
    pkg.status = 'checking';
    updateInstalledDisplay(installedPackages);

    try {
        const response = await fetch('/api/packages/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                host, username, password,
                packages: [pkg]
            })
        });

        const result = await response.json();

        if (result.success && result.packages.length > 0) {
            const updatedPkg = result.packages[0];
            const index = installedPackages.findIndex(p => p.file === file);
            if (index >= 0) {
                installedPackages[index] = updatedPkg;
                updateInstalledDisplay(installedPackages);
            }
        }
    } catch (error) {
        console.error('验证包状态失败:', error);
        pkg.status = 'unknown';
        updateInstalledDisplay(installedPackages);
    }
}





// 卸载选中的包
async function uninstallSelected() {
    // 获取可用包列表中选中的已安装包
    const selectedCheckboxes = document.querySelectorAll('.package-item-checkbox:checked');
    const selectedInstalledForUninstall = [];

    selectedCheckboxes.forEach(checkbox => {
        const file = checkbox.dataset.file;
        const pkg = packages.find(p => p.file === file);
        const installedPkg = installedPackages.find(installed => installed.file === file);

        // 只处理已安装的包
        if (pkg && installedPkg) {
            selectedInstalledForUninstall.push({
                name: pkg.name,
                file: pkg.file,
                description: pkg.description,
                folder: pkg.folder,
                status: installedPkg.status
            });
        }
    });

    if (selectedInstalledForUninstall.length === 0) {
        showToast('请先选择要卸载的已安装软件包', 'error');
        return;
    }

    if (!confirm(`确定要卸载 ${selectedInstalledForUninstall.length} 个软件包吗？\n\n${selectedInstalledForUninstall.map(p => p.name).join('\n')}`)) {
        return;
    }

    await executeUninstall(selectedInstalledForUninstall);

    // 卸载完成后刷新包列表
    await refreshPackages();
    await refreshInstalledPackages();
}

// 检查单个包状态
async function checkSinglePackage(file) {
    const pkg = installedPackages.find(p => p.file === file);
    if (!pkg) return;

    if (!isConnected) {
        showToast('请先测试SSH连接', 'error');
        return;
    }

    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    // 构建检查命令
    let checkCommand;
    if (pkg.type === 'python') {
        checkCommand = `cd /home/<USER>/server_install && python3 packages/${pkg.file} status`;
    } else {
        checkCommand = `cd /home/<USER>/server_install && bash packages/${pkg.file} status`;
    }

    // 构建终端URL，包含要执行的命令
    const terminalUrl = `/terminal?host=${encodeURIComponent(host)}&username=${encodeURIComponent(username)}&password=${encodeURIComponent(btoa(password))}&command=${encodeURIComponent(checkCommand)}&operation=check&package=${encodeURIComponent(pkg.name)}`;

    // 在新窗口打开终端并执行检查命令
    window.open(terminalUrl, '_blank', 'width=1200,height=700,scrollbars=yes,resizable=yes');

    showToast(`已在新终端窗口中开始检查 "${pkg.name}" 状态`);
}

// 卸载单个包
async function uninstallSinglePackage(file) {
    const pkg = installedPackages.find(p => p.file === file);
    if (!pkg) return;

    if (!confirm(`确定要卸载 "${pkg.name}" 吗？`)) {
        return;
    }

    await executeUninstall([pkg]);
}

// 执行卸载操作
async function executeUninstall(packagesToUninstall) {
    if (!isConnected) {
        showToast('请先测试SSH连接', 'error');
        return;
    }

    const host = document.getElementById('host').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    const uninstallBtn = document.querySelector('.uninstall-selected-btn');
    if (uninstallBtn) {
        uninstallBtn.disabled = true;
        uninstallBtn.textContent = '🗑️ 准备卸载...';
    }

    try {
        // 首先准备卸载队列文件
        const response = await fetch('/api/uninstall/prepare', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                host,
                username,
                password,
                packages: packagesToUninstall
            })
        });

        const result = await response.json();

        if (result.success) {
            // 构建卸载命令，包含用户名和密码参数以避免手动输入
            const uninstallCommand = `cd /home/<USER>/server_install && python3 main_installer.py uninstall_queue.json uninstall "${username}" "${password}"`;

            // 将包信息编码为JSON字符串
            const packagesJson = encodeURIComponent(JSON.stringify(packagesToUninstall));

            // 构建终端URL，包含要执行的命令、操作类型和包信息
            const terminalUrl = `/terminal?host=${encodeURIComponent(host)}&username=${encodeURIComponent(username)}&password=${encodeURIComponent(btoa(password))}&command=${encodeURIComponent(uninstallCommand)}&operation=uninstall&packages=${packagesJson}`;

            // 在新窗口打开终端并执行卸载命令
            window.open(terminalUrl, '_blank', 'width=1200,height=700,scrollbars=yes,resizable=yes');

            showToast('已在新终端窗口中开始卸载，请查看终端输出');

            // 清除选中状态
            const checkboxes = document.querySelectorAll('.package-item-checkbox:checked');
            checkboxes.forEach(cb => cb.checked = false);
            updateUninstallButtonState();
        } else {
            showToast(result.message || '准备卸载失败', 'error');
        }
    } catch (error) {
        showToast('准备卸载失败: ' + error.message, 'error');
    } finally {
        if (uninstallBtn) {
            uninstallBtn.disabled = false;
            uninstallBtn.textContent = '🗑️ 卸载选中';
        }
        updateUninstallButtonState();
    }
}



// 停止定期验证
function stopPeriodicVerification() {
    if (verificationInterval) {
        clearInterval(verificationInterval);
        verificationInterval = null;
    }
}

// 旧的startUninstall函数已移除，现在使用已安装包列表进行卸载

// 原有的显示卸载进度函数已移除
// 卸载过程现在在终端中实时显示





// 初始化主页面WebSocket连接
function initMainWebSocket() {
    if (typeof io === 'undefined') {
        console.log('Socket.IO未加载，跳过WebSocket初始化');
        return;
    }

    try {
        mainSocket = io();

        mainSocket.on('connect', () => {
            console.log('主页面WebSocket连接成功');
        });

        mainSocket.on('disconnect', () => {
            console.log('主页面WebSocket连接断开');
        });

        mainSocket.on('connect_error', (error) => {
            console.log('WebSocket连接失败:', error);
        });

    } catch (error) {
        console.log('WebSocket初始化失败:', error);
    }
}